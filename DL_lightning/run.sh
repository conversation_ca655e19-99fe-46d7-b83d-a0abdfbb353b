#!/bin/bash

# 检查是否提供了脚本路径作为第一个参数
if [ -z "$1" ]; then
  echo "错误：请提供要执行的Python脚本的路径作为第一个参数。"
  echo "用法: ./run.sh <path_to_script.py> [hydra_args...]"
  exit 1
fi

# 第一个参数是脚本路径
SCRIPT_PATH="$1"
# 移除第一个参数，剩下的就是Hydra的参数
shift

# 设置项目根目录为PYTHONPATH的一部分
export PYTHONPATH=${PYTHONPATH}:$(pwd)

# 设置 HYDRA_FULL_ERROR=1 以获取详细的错误堆栈信息
export HYDRA_FULL_ERROR=1

# 执行指定的脚本，并将所有剩余的参数 ($@) 传递给它
echo "Executing '$SCRIPT_PATH' with arguments: $@"
python "$SCRIPT_PATH" "$@"