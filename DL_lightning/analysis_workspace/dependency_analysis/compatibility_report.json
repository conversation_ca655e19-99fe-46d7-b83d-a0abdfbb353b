{"key_dependencies": {"lightning": {"current": "2.5.2", "required": ">=2.3.0", "status": "installed"}, "torch": {"current": "2.7.1", "required": ">=2.3.1", "status": "installed"}, "torchmetrics": {"current": "1.7.4", "required": ">=1.4.0", "status": "installed"}, "hydra-core": {"current": "1.3.2", "required": ">=1.3.2", "status": "installed"}, "omegaconf": {"current": "2.3.0", "required": ">=2.3.0", "status": "installed"}, "wandb": {"current": "0.21.0", "required": ">=0.17.0", "status": "installed"}, "ray": {"current": "2.47.1", "required": ">=2.10.0", "status": "installed"}, "ray-lightning": {"current": "0.3.0", "required": ">=0.2.0", "status": "installed"}}, "lightning_compatibility": {"lightning_version": "2.5.2", "torch_version": "2.7.1+cu126", "torchmetrics_version": "1.7.4", "lightning_torch_compat": "compatible", "lightning_metrics_compat": "compatible", "scheduler_issue_analysis": "pending"}, "scheduler_diagnosis": {"issue_type": "functools.partial compatibility", "root_cause": "Lightning expects instantiated scheduler, not partial object", "affected_component": "SegmentationModule.configure_optimizers()", "lightning_expectation": "scheduler.optimizer attribute must exist", "current_behavior": "partial object has no optimizer attribute", "fix_strategy": "Remove _partial_=True or handle partial instantiation properly"}, "pip_check_result": {"success": false, "output": "lightning 2.5.2 requires pytorch-lightning, which is not installed.\nray-lightning 0.3.0 requires pytorch-lightning, which is not installed.\n"}}