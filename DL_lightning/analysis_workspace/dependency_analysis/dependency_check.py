#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
依赖兼容性深度分析脚本
分析 DL_lightning 项目的依赖版本矩阵和兼容性问题
"""

import subprocess
import sys
import pkg_resources
from typing import Dict, List, Tuple
import json

def get_installed_packages() -> Dict[str, str]:
    """获取已安装包的版本信息"""
    installed_packages = {}
    for dist in pkg_resources.working_set:
        installed_packages[dist.project_name.lower()] = dist.version
    return installed_packages

def check_key_dependencies() -> Dict[str, Dict]:
    """检查关键依赖的版本兼容性"""
    key_deps = {
        'lightning': {'current': None, 'required': '>=2.3.0', 'status': 'unknown'},
        'torch': {'current': None, 'required': '>=2.3.1', 'status': 'unknown'},
        'torchmetrics': {'current': None, 'required': '>=1.4.0', 'status': 'unknown'},
        'hydra-core': {'current': None, 'required': '>=1.3.2', 'status': 'unknown'},
        'omegaconf': {'current': None, 'required': '>=2.3.0', 'status': 'unknown'},
        'wandb': {'current': None, 'required': '>=0.17.0', 'status': 'unknown'},
        'ray': {'current': None, 'required': '>=2.10.0', 'status': 'unknown'},
        'ray-lightning': {'current': None, 'required': '>=0.2.0', 'status': 'unknown'},
    }
    
    installed = get_installed_packages()
    
    for pkg_name, info in key_deps.items():
        pkg_key = pkg_name.replace('-', '_').lower()
        if pkg_key in installed:
            info['current'] = installed[pkg_key]
            info['status'] = 'installed'
        elif pkg_name.lower() in installed:
            info['current'] = installed[pkg_name.lower()]
            info['status'] = 'installed'
        else:
            info['status'] = 'missing'
    
    return key_deps

def analyze_lightning_compatibility() -> Dict[str, str]:
    """分析 Lightning 相关组件的兼容性"""
    try:
        import lightning
        import torch
        import torchmetrics
        
        compatibility_report = {
            'lightning_version': lightning.__version__,
            'torch_version': torch.__version__,
            'torchmetrics_version': torchmetrics.__version__,
            'lightning_torch_compat': 'unknown',
            'lightning_metrics_compat': 'unknown',
            'scheduler_issue_analysis': 'pending'
        }
        
        # 检查 Lightning 与 PyTorch 兼容性
        lightning_major = int(lightning.__version__.split('.')[0])
        torch_major = int(torch.__version__.split('.')[0])
        
        if lightning_major >= 2 and torch_major >= 2:
            compatibility_report['lightning_torch_compat'] = 'compatible'
        else:
            compatibility_report['lightning_torch_compat'] = 'potential_issue'
        
        # 检查 TorchMetrics 兼容性
        metrics_major = int(torchmetrics.__version__.split('.')[0])
        if metrics_major >= 1:
            compatibility_report['lightning_metrics_compat'] = 'compatible'
        else:
            compatibility_report['lightning_metrics_compat'] = 'outdated'
        
        return compatibility_report
        
    except ImportError as e:
        return {'error': f'Import failed: {e}'}

def diagnose_scheduler_issue() -> Dict[str, str]:
    """诊断调度器兼容性问题"""
    diagnosis = {
        'issue_type': 'functools.partial compatibility',
        'root_cause': 'Lightning expects instantiated scheduler, not partial object',
        'affected_component': 'SegmentationModule.configure_optimizers()',
        'lightning_expectation': 'scheduler.optimizer attribute must exist',
        'current_behavior': 'partial object has no optimizer attribute',
        'fix_strategy': 'Remove _partial_=True or handle partial instantiation properly'
    }
    
    return diagnosis

def run_pip_check() -> Tuple[bool, str]:
    """运行 pip check 检查依赖冲突"""
    try:
        result = subprocess.run(['pip', 'check'], 
                              capture_output=True, 
                              text=True, 
                              timeout=30)
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "pip check timed out"
    except Exception as e:
        return False, f"pip check failed: {e}"

def main():
    """主分析函数"""
    print("=" * 80)
    print("DL_Lightning 依赖兼容性深度分析")
    print("=" * 80)
    
    # 1. 检查关键依赖
    print("\n1. 关键依赖版本检查:")
    key_deps = check_key_dependencies()
    for pkg, info in key_deps.items():
        status_icon = "✅" if info['status'] == 'installed' else "❌"
        print(f"  {status_icon} {pkg}: {info['current']} (要求: {info['required']})")
    
    # 2. Lightning 兼容性分析
    print("\n2. Lightning 兼容性分析:")
    lightning_compat = analyze_lightning_compatibility()
    for key, value in lightning_compat.items():
        print(f"  {key}: {value}")
    
    # 3. 调度器问题诊断
    print("\n3. 调度器问题诊断:")
    scheduler_diagnosis = diagnose_scheduler_issue()
    for key, value in scheduler_diagnosis.items():
        print(f"  {key}: {value}")
    
    # 4. pip check 结果
    print("\n4. 依赖冲突检查:")
    pip_ok, pip_output = run_pip_check()
    if pip_ok:
        print("  ✅ 无依赖冲突")
    else:
        print("  ⚠️ 发现依赖问题:")
        print(f"  {pip_output}")
    
    # 5. 生成分析报告
    analysis_report = {
        'key_dependencies': key_deps,
        'lightning_compatibility': lightning_compat,
        'scheduler_diagnosis': scheduler_diagnosis,
        'pip_check_result': {'success': pip_ok, 'output': pip_output}
    }
    
    # 保存报告
    with open('analysis_workspace/dependency_analysis/compatibility_report.json', 'w') as f:
        json.dump(analysis_report, f, indent=2)
    
    print(f"\n📊 详细报告已保存到: analysis_workspace/dependency_analysis/compatibility_report.json")

if __name__ == "__main__":
    main()
