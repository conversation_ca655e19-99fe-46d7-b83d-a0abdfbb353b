#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复效果验证测试脚本
验证调度器兼容性修复是否成功
"""

import sys
import torch
from pathlib import Path
from omegaconf import DictConfig, OmegaConf

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import lightning.pytorch as pl
from src.models.segmentation_module import SegmentationModule
from src.data.remote_sensing_datamodule import RemoteSensingDataModule
from src.utils.hydra_resolvers import register_hydra_resolvers

# 注册自定义解析器
register_hydra_resolvers()

def test_scheduler_instantiation():
    """测试调度器实例化"""
    print("=== 测试调度器实例化 ===")
    
    # 创建测试配置
    optimizer_cfg = OmegaConf.create({
        '_target_': 'torch.optim.Adam',
        'lr': 0.001
    })
    
    scheduler_cfg = OmegaConf.create({
        '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
        'T_max': 100,
        'eta_min': 1e-6
    })
    
    loss_cfg = OmegaConf.create({
        '_target_': 'torch.nn.CrossEntropyLoss',
        'ignore_index': 255
    })
    
    model_params = OmegaConf.create({
        'in_channels': 3,
        'pretrained': False
    })
    
    try:
        # 创建SegmentationModule
        model = SegmentationModule(
            model_name='deeplabv3plus',
            model_params=model_params,
            optimizer_cfg=optimizer_cfg,
            scheduler_cfg=scheduler_cfg,
            loss_cfg=loss_cfg,
            num_classes=14,
            ignore_index=255
        )
        print("✅ SegmentationModule 创建成功")
        
        # 测试 configure_optimizers
        optimizer_config = model.configure_optimizers()
        print("✅ configure_optimizers 调用成功")
        
        # 验证返回结构
        assert isinstance(optimizer_config, dict), "应该返回字典"
        assert 'optimizer' in optimizer_config, "应该包含 optimizer"
        assert 'lr_scheduler' in optimizer_config, "应该包含 lr_scheduler"
        
        optimizer = optimizer_config['optimizer']
        scheduler_config = optimizer_config['lr_scheduler']
        scheduler = scheduler_config['scheduler']
        
        print(f"✅ 优化器类型: {type(optimizer).__name__}")
        print(f"✅ 调度器类型: {type(scheduler).__name__}")
        
        # 关键验证：检查调度器是否有 optimizer 属性
        assert hasattr(scheduler, 'optimizer'), "调度器应该有 optimizer 属性"
        print("✅ 调度器具有 optimizer 属性")
        
        # 验证调度器的 optimizer 是否正确
        assert scheduler.optimizer is optimizer, "调度器的 optimizer 应该与传入的一致"
        print("✅ 调度器的 optimizer 引用正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lightning_trainer_integration():
    """测试与Lightning Trainer的集成"""
    print("\n=== 测试Lightning Trainer集成 ===")
    
    try:
        # 创建简化的数据模块
        datamodule = RemoteSensingDataModule(
            data_dir="../Data_SRC/Dataset_v2.1",
            image_size=256,
            num_classes=14,
            batch_size=2,
            num_workers=1,
            persistent_workers=False
        )
        print("✅ DataModule 创建成功")
        
        # 创建模型
        optimizer_cfg = OmegaConf.create({
            '_target_': 'torch.optim.Adam',
            'lr': 0.001
        })
        
        scheduler_cfg = OmegaConf.create({
            '_target_': 'torch.optim.lr_scheduler.StepLR',
            'step_size': 10,
            'gamma': 0.1
        })
        
        loss_cfg = OmegaConf.create({
            '_target_': 'torch.nn.CrossEntropyLoss',
            'ignore_index': 255
        })
        
        model_params = OmegaConf.create({
            'in_channels': 3,
            'pretrained': False
        })
        
        model = SegmentationModule(
            model_name='deeplabv3plus',
            model_params=model_params,
            optimizer_cfg=optimizer_cfg,
            scheduler_cfg=scheduler_cfg,
            loss_cfg=loss_cfg,
            num_classes=14,
            ignore_index=255
        )
        print("✅ SegmentationModule 创建成功")
        
        # 创建Trainer（关键测试点）
        trainer = pl.Trainer(
            accelerator='auto',
            devices=1,
            max_epochs=1,
            fast_dev_run=True,  # 只运行一个批次
            enable_progress_bar=False,
            enable_model_summary=False,
            logger=False,
            enable_checkpointing=False
        )
        print("✅ Trainer 创建成功")
        
        # 关键测试：trainer.fit() 应该不会因为调度器问题而失败
        print("开始训练测试...")
        trainer.fit(model=model, datamodule=datamodule)
        print("✅ 训练测试完成，无调度器错误")
        
        return True
        
    except Exception as e:
        print(f"❌ Lightning集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 测试配置文件加载 ===")
    
    try:
        import hydra
        from hydra import compose, initialize_config_dir
        from hydra.core.global_hydra import GlobalHydra
        
        # 清理之前的Hydra实例
        GlobalHydra.instance().clear()
        
        # 初始化Hydra
        config_dir = str(project_root / "configs")
        with initialize_config_dir(config_dir=config_dir, version_base=None):
            # 测试基本配置加载
            cfg = compose(config_name="config.yaml")
            print("✅ 基本配置加载成功")
            
            # 测试优化器配置
            optimizer = hydra.utils.instantiate(
                cfg.optimizer,
                params=[torch.nn.Parameter(torch.randn(10, 10))],
                _partial_=False
            )
            print(f"✅ 优化器实例化成功: {type(optimizer).__name__}")
            
            # 测试调度器配置
            scheduler = hydra.utils.instantiate(
                cfg.scheduler,
                optimizer=optimizer,
                _partial_=False
            )
            print(f"✅ 调度器实例化成功: {type(scheduler).__name__}")
            
            # 验证调度器属性
            assert hasattr(scheduler, 'optimizer'), "调度器应该有 optimizer 属性"
            print("✅ 调度器具有正确的属性")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("DL_Lightning 修复效果验证测试")
    print("=" * 80)
    
    # 测试1: 调度器实例化
    test1_success = test_scheduler_instantiation()
    
    # 测试2: 配置文件加载
    test2_success = test_config_loading()
    
    # 测试3: Lightning Trainer集成
    test3_success = test_lightning_trainer_integration()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    print(f"调度器实例化测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"配置文件加载测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"Lightning集成测试: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    all_passed = test1_success and test2_success and test3_success
    
    if all_passed:
        print("\n🎉 所有测试通过！调度器兼容性问题已修复。")
        print("✅ 项目现在可以正常进行训练了。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
    
    return all_passed

if __name__ == "__main__":
    main()
