#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置系统混乱度诊断脚本
扫描 DL_lightning 项目中的配置冲突和不一致问题
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import defaultdict

class ConfigConflictScanner:
    def __init__(self, config_root: str):
        self.config_root = Path(config_root)
        self.conflicts = defaultdict(list)
        self.duplicate_keys = defaultdict(list)
        self.target_paths = defaultdict(list)
        
    def scan_yaml_files(self) -> List[Path]:
        """扫描所有 YAML 配置文件"""
        yaml_files = []
        for root, dirs, files in os.walk(self.config_root):
            for file in files:
                if file.endswith(('.yaml', '.yml')):
                    yaml_files.append(Path(root) / file)
        return yaml_files
    
    def load_yaml_safe(self, file_path: Path) -> Dict[str, Any]:
        """安全加载 YAML 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"⚠️ 无法加载 {file_path}: {e}")
            return {}
    
    def check_duplicate_keys(self, configs: Dict[Path, Dict]) -> Dict[str, List]:
        """检查重复键值"""
        key_files = defaultdict(list)
        
        for file_path, config in configs.items():
            self._extract_keys(config, file_path, key_files)
        
        # 找出重复的键
        duplicates = {}
        for key, files in key_files.items():
            if len(files) > 1:
                duplicates[key] = files
        
        return duplicates
    
    def _extract_keys(self, config: Dict, file_path: Path, key_files: Dict, prefix: str = ""):
        """递归提取配置键"""
        if not isinstance(config, dict):
            return
            
        for key, value in config.items():
            full_key = f"{prefix}.{key}" if prefix else key
            key_files[full_key].append(str(file_path))
            
            if isinstance(value, dict):
                self._extract_keys(value, file_path, key_files, full_key)
    
    def check_target_consistency(self, configs: Dict[Path, Dict]) -> Dict[str, List]:
        """检查 _target_ 路径的一致性"""
        target_issues = defaultdict(list)
        
        for file_path, config in configs.items():
            self._check_targets(config, file_path, target_issues)
        
        return dict(target_issues)
    
    def _check_targets(self, config: Dict, file_path: Path, issues: Dict, path: str = ""):
        """递归检查 _target_ 配置"""
        if not isinstance(config, dict):
            return
            
        for key, value in config.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "_target_":
                # 检查 _target_ 路径是否有效
                if isinstance(value, str):
                    if not self._validate_target_path(value):
                        issues["invalid_target"].append({
                            "file": str(file_path),
                            "path": current_path,
                            "target": value
                        })
                    
                    # 记录所有 _target_ 路径
                    self.target_paths[value].append(str(file_path))
            
            elif isinstance(value, dict):
                self._check_targets(value, file_path, issues, current_path)
    
    def _validate_target_path(self, target: str) -> bool:
        """验证 _target_ 路径是否可能有效"""
        # 基本格式检查
        if not target or "." not in target:
            return False
        
        # 检查是否是已知的有效路径
        valid_prefixes = [
            "torch.optim",
            "torch.optim.lr_scheduler",
            "lightning.pytorch",
            "src.models",
            "src.data",
            "src.losses",
            "src.callbacks",
            "ray.tune",
            "ray.init"
        ]
        
        return any(target.startswith(prefix) for prefix in valid_prefixes)
    
    def check_partial_usage(self, configs: Dict[Path, Dict]) -> Dict[str, List]:
        """检查 _partial_ 使用规范"""
        partial_issues = defaultdict(list)
        
        for file_path, config in configs.items():
            self._check_partial(config, file_path, partial_issues)
        
        return dict(partial_issues)
    
    def _check_partial(self, config: Dict, file_path: Path, issues: Dict, path: str = ""):
        """递归检查 _partial_ 配置"""
        if not isinstance(config, dict):
            return
            
        for key, value in config.items():
            current_path = f"{path}.{key}" if path else key
            
            if key == "_partial_" and value is True:
                # 检查是否同时有 _target_
                if "_target_" in config:
                    target = config["_target_"]
                    # 检查是否是调度器相关
                    if "lr_scheduler" in target:
                        issues["scheduler_partial"].append({
                            "file": str(file_path),
                            "path": current_path,
                            "target": target,
                            "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"
                        })
                else:
                    issues["partial_without_target"].append({
                        "file": str(file_path),
                        "path": current_path
                    })
            
            elif isinstance(value, dict):
                self._check_partial(value, file_path, issues, current_path)
    
    def generate_report(self) -> Dict[str, Any]:
        """生成完整的诊断报告"""
        print("🔍 扫描配置文件...")
        yaml_files = self.scan_yaml_files()
        print(f"发现 {len(yaml_files)} 个配置文件")
        
        # 加载所有配置
        configs = {}
        for file_path in yaml_files:
            configs[file_path] = self.load_yaml_safe(file_path)
        
        print("🔍 检查重复键值...")
        duplicate_keys = self.check_duplicate_keys(configs)
        
        print("🔍 检查 _target_ 一致性...")
        target_issues = self.check_target_consistency(configs)
        
        print("🔍 检查 _partial_ 使用规范...")
        partial_issues = self.check_partial_usage(configs)
        
        # 生成报告
        report = {
            "scan_summary": {
                "total_files": len(yaml_files),
                "files_scanned": [str(f) for f in yaml_files],
                "duplicate_key_count": len(duplicate_keys),
                "target_issue_count": sum(len(issues) for issues in target_issues.values()),
                "partial_issue_count": sum(len(issues) for issues in partial_issues.values())
            },
            "duplicate_keys": duplicate_keys,
            "target_issues": target_issues,
            "partial_issues": partial_issues,
            "target_usage_stats": {target: len(files) for target, files in self.target_paths.items()}
        }
        
        return report

def main():
    """主函数"""
    print("=" * 80)
    print("DL_Lightning 配置系统混乱度诊断")
    print("=" * 80)
    
    scanner = ConfigConflictScanner("configs")
    report = scanner.generate_report()
    
    # 打印摘要
    print(f"\n📊 扫描摘要:")
    print(f"  配置文件总数: {report['scan_summary']['total_files']}")
    print(f"  重复键数量: {report['scan_summary']['duplicate_key_count']}")
    print(f"  _target_ 问题: {report['scan_summary']['target_issue_count']}")
    print(f"  _partial_ 问题: {report['scan_summary']['partial_issue_count']}")
    
    # 显示关键问题
    if report['partial_issues'].get('scheduler_partial'):
        print(f"\n⚠️ 发现调度器 _partial_ 问题:")
        for issue in report['partial_issues']['scheduler_partial']:
            print(f"  文件: {issue['file']}")
            print(f"  目标: {issue['target']}")
            print(f"  问题: {issue['issue']}")
    
    # 保存详细报告
    report_path = "analysis_workspace/config_audit/config_conflict_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_path}")

if __name__ == "__main__":
    main()
