{"scan_summary": {"total_files": 24, "files_scanned": ["configs/config.yaml", "configs/trainer/default.yaml", "configs/loss/dice.yaml", "configs/loss/combined.yaml", "configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml", "configs/loss/ce_dice.yaml", "configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml", "configs/scheduler/poly.yaml", "configs/scheduler/cosine.yaml", "configs/logger/wandb.yaml", "configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml", "configs/optimizer/sgd.yaml", "configs/optimizer/adamw.yaml", "configs/experiment/hpo_search_space.yaml", "configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml", "configs/callbacks/default.yaml"], "duplicate_key_count": 69, "target_issue_count": 0, "partial_issue_count": 7}, "duplicate_keys": {"project_name": ["configs/config.yaml", "configs/hpo/tune_basic.yaml"], "experiment_name": ["configs/config.yaml", "configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "run_name": ["configs/config.yaml", "configs/hpo/tune_basic.yaml"], "wandb": ["configs/config.yaml", "configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "wandb.project": ["configs/config.yaml", "configs/hpo/tune_basic.yaml"], "wandb.name": ["configs/config.yaml", "configs/hpo/tune_basic.yaml"], "wandb.tags": ["configs/config.yaml", "configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "wandb.notes": ["configs/config.yaml", "configs/hpo/tune_basic.yaml"], "_target_": ["configs/trainer/default.yaml", "configs/loss/dice.yaml", "configs/loss/combined.yaml", "configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml", "configs/loss/ce_dice.yaml", "configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml", "configs/scheduler/cosine.yaml", "configs/logger/wandb.yaml", "configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml", "configs/optimizer/adamw.yaml"], "num_classes": ["configs/loss/dice.yaml", "configs/loss/combined.yaml", "configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml", "configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml", "configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "ignore_index": ["configs/loss/dice.yaml", "configs/loss/combined.yaml", "configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml", "configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml", "configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "reduction": ["configs/loss/dice.yaml", "configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml"], "use_dynamic_weights": ["configs/loss/combined.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml"], "weight": ["configs/loss/lovasz.yaml", "configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml"], "weight_method": ["configs/loss/cross_entropy.yaml", "configs/loss/focal.yaml"], "model_name": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "model_params": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "model_params.backbone": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/deeplabv3.yaml"], "model_params.in_channels": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "model_params.pretrained": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg._target_": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg._partial_": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg.lr": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg.weight_decay": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "optimizer_cfg.betas": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg._target_": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg._partial_": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg.T_0": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg.T_mult": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg.eta_min": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "loss_cfg": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml"], "name": ["configs/model/modular_deeplabv3plus.yaml", "configs/model/unet.yaml", "configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml", "configs/model/unetpp.yaml", "configs/logger/wandb.yaml"], "model_params.features": ["configs/model/unet.yaml", "configs/model/unetpp.yaml"], "model_params.bilinear": ["configs/model/unet.yaml", "configs/model/unetpp.yaml"], "scheduler_cfg.T_max": ["configs/model/deeplabv3.yaml", "configs/model/swin_unet.yaml"], "_partial_": ["configs/scheduler/cosine.yaml", "configs/optimizer/adamw.yaml"], "data_dir": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "image_size": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "training_level": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "sampling_strategy": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "use_multiscale": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "scale_weights": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "scale_weights.1_1": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "scale_weights.1_2": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "scale_weights.1_0.5": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "batch_size": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "val_batch_size": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "test_batch_size": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "num_workers": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "pin_memory": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "persistent_workers": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "prefetch_factor": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "mean": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "std": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "random_crop": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "random_rotate90": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "flip": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "transpose": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "random_brightness_contrast": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "brightness_limit": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "contrast_limit": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "cutout": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "cutout_max_size": ["configs/data/remote_sensing.yaml", "configs/data/suide_v2.1.yaml"], "trainer": ["configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "trainer.max_epochs": ["configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "optimizer": ["configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"], "optimizer.lr": ["configs/experiment/baseline_unet.yaml", "configs/hpo/tune_basic.yaml"]}, "target_issues": {}, "partial_issues": {"scheduler_partial": [{"file": "configs/model/modular_deeplabv3plus.yaml", "path": "scheduler_cfg._partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingWarmRestarts", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/model/unet.yaml", "path": "scheduler_cfg._partial_", "target": "torch.optim.lr_scheduler.StepLR", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/model/deeplabv3.yaml", "path": "scheduler_cfg._partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingLR", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/model/swin_unet.yaml", "path": "scheduler_cfg._partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingLR", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/model/unetpp.yaml", "path": "scheduler_cfg._partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingWarmRestarts", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/scheduler/cosine.yaml", "path": "_partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingLR", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}, {"file": "configs/hpo/tune_basic.yaml", "path": "scheduler._partial_", "target": "torch.optim.lr_scheduler.CosineAnnealingLR", "issue": "Scheduler with _partial_=True may cause Lightning compatibility issues"}]}, "target_usage_stats": {"lightning.pytorch.Trainer": 2, "src.losses.dice_loss.DiceLoss": 1, "src.losses.combined_loss.CombinedLoss": 1, "src.losses.lovasz_loss.LovaszLoss": 1, "src.losses.cross_entropy_loss.CrossEntropyLoss": 1, "src.losses.focal_loss.FocalLoss": 1, "src.losses.dice_loss.CEDiceLoss": 2, "src.models.segmentation_module.SegmentationModule": 6, "torch.optim.AdamW": 6, "torch.optim.lr_scheduler.CosineAnnealingWarmRestarts": 2, "torch.optim.Adam": 1, "torch.optim.lr_scheduler.StepLR": 1, "torch.optim.lr_scheduler.CosineAnnealingLR": 4, "lightning.pytorch.loggers.WandbLogger": 2, "src.data.remote_sensing_datamodule.RemoteSensingDataModule": 3, "src.models.architectures.unet.UNet": 1, "lightning.pytorch.callbacks.ModelCheckpoint": 2, "lightning.pytorch.callbacks.LearningRateMonitor": 2, "lightning.pytorch.callbacks.RichModelSummary": 2, "ray.init": 1, "ray.tune.schedulers.ASHAScheduler": 1}}