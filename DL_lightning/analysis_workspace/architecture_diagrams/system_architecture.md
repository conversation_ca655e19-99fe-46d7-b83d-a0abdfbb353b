# DL_Lightning 系统架构可视化分析

## 1. 主流程图：从启动到训练完成

```mermaid
graph TD
    A[scripts/train.py 启动] --> B[注册 Hydra 解析器]
    B --> C[加载 Hydra 配置]
    C --> D[解析配置组合]
    D --> E[实例化 DataModule]
    E --> F[实例化 SegmentationModule]
    F --> G[实例化 Callbacks]
    G --> H[实例化 Logger]
    H --> I[实例化 Trainer]
    I --> J[trainer.fit()]
    J --> K[Lightning 内部流程]
    K --> L{调度器验证}
    L -->|失败| M[AttributeError: partial object]
    L -->|成功| N[开始训练]
    N --> O[训练完成]
    
    style M fill:#ff9999
    style L fill:#ffcc99
```

## 2. 调度器问题诊断图

```mermaid
graph TD
    A[SegmentationModule.__init__] --> B[保存 scheduler_cfg]
    B --> C[trainer.fit() 调用]
    C --> D[Lightning 内部 setup]
    D --> E[调用 configure_optimizers()]
    E --> F[hydra.utils.instantiate(scheduler_cfg)]
    F --> G{_partial_=True?}
    G -->|是| H[返回 functools.partial 对象]
    G -->|否| I[返回实例化的调度器]
    H --> J[Lightning 验证调度器]
    I --> J
    J --> K{检查 scheduler.optimizer}
    K -->|partial 对象| L[❌ AttributeError]
    K -->|实例对象| M[✅ 验证通过]
    
    style L fill:#ff9999
    style H fill:#ffcc99
    style G fill:#ffffcc
```

## 3. 配置解析流程图

```mermaid
graph TD
    A[config.yaml 加载] --> B[defaults 机制]
    B --> C[组合配置文件]
    C --> D[optimizer: adamw]
    C --> E[scheduler: cosine]
    C --> F[model: deeplabv3]
    D --> G[adamw.yaml<br/>_partial_: true]
    E --> H[cosine.yaml<br/>_partial_: true]
    F --> I[deeplabv3.yaml]
    G --> J[Hydra 解析]
    H --> J
    I --> J
    J --> K[最终配置对象]
    K --> L[传递给组件]
    
    style G fill:#ffcc99
    style H fill:#ffcc99
```

## 4. 数据流架构图

```mermaid
graph LR
    A[RemoteSensingDataModule] --> B[SuiDeDatasetV2]
    B --> C[AlbumentationsWrapper]
    C --> D[DataLoader]
    D --> E[SegmentationModule]
    E --> F[DeepLabV3Plus]
    F --> G[Loss Calculation]
    G --> H[Optimizer Step]
    H --> I{Scheduler Step}
    I -->|成功| J[指标计算]
    I -->|失败| K[❌ 训练中断]
    J --> L[WandB 记录]
    
    style I fill:#ffcc99
    style K fill:#ff9999
```

## 5. 错误传播路径图

```mermaid
graph TD
    A[trainer.fit() 开始] --> B[strategy.setup()]
    B --> C[setup_optimizers()]
    C --> D[_init_optimizers_and_lr_schedulers()]
    D --> E[调用 configure_optimizers()]
    E --> F[返回配置字典]
    F --> G[_validate_optimizers_attached()]
    G --> H[检查 config.scheduler.optimizer]
    H --> I{scheduler 是 partial?}
    I -->|是| J[❌ AttributeError: 'functools.partial' object has no attribute 'optimizer']
    I -->|否| K[✅ 验证通过]
    
    style J fill:#ff9999
    style I fill:#ffcc99
    
    J --> L[错误堆栈]
    L --> M[File: optimizer.py:368]
    M --> N[if config.scheduler.optimizer not in optimizers]
```

## 问题根因分析

### 核心问题
1. **配置设计缺陷**: `_partial_=True` 与 Lightning 期望不匹配
2. **组件耦合**: Lightning 内部验证逻辑假设调度器已实例化
3. **配置传递**: Hydra instantiate 返回 partial 对象而非实例

### 影响范围
- 阻止完整训练流程
- 影响所有使用调度器的实验
- 破坏 Lightning 的自动优化功能

### 修复策略
1. **立即修复**: 移除 `_partial_=True` 配置
2. **架构改进**: 重新设计 configure_optimizers 方法
3. **测试验证**: 确保修复不影响其他功能
