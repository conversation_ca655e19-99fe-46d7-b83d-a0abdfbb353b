#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调度器兼容性修复脚本
解决 DL_lightning 项目中的调度器 _partial_ 兼容性问题
"""

import os
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any

class SchedulerCompatibilityFixer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "analysis_workspace" / "fix_implementations" / "backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def backup_file(self, file_path: Path) -> Path:
        """备份原始文件"""
        backup_path = self.backup_dir / f"{file_path.name}.backup"
        shutil.copy2(file_path, backup_path)
        print(f"📁 备份文件: {file_path} -> {backup_path}")
        return backup_path
    
    def fix_scheduler_configs(self):
        """修复调度器配置文件中的 _partial_ 问题"""
        scheduler_files = [
            "configs/scheduler/cosine.yaml",
            "configs/scheduler/poly.yaml"
        ]
        
        for file_path in scheduler_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.backup_file(full_path)
                self._remove_partial_from_file(full_path)
    
    def fix_model_configs(self):
        """修复模型配置文件中的调度器 _partial_ 问题"""
        model_files = [
            "configs/model/deeplabv3.yaml",
            "configs/model/unet.yaml",
            "configs/model/unetpp.yaml",
            "configs/model/swin_unet.yaml",
            "configs/model/modular_deeplabv3plus.yaml"
        ]
        
        for file_path in model_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.backup_file(full_path)
                self._fix_model_scheduler_config(full_path)
    
    def _remove_partial_from_file(self, file_path: Path):
        """从配置文件中移除 _partial_ 设置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除 _partial_: true 行
            lines = content.split('\n')
            filtered_lines = []
            
            for line in lines:
                if '_partial_:' not in line and '_partial_ :' not in line:
                    filtered_lines.append(line)
                else:
                    print(f"  移除行: {line.strip()}")
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(filtered_lines))
            
            print(f"✅ 修复完成: {file_path}")
            
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")
    
    def _fix_model_scheduler_config(self, file_path: Path):
        """修复模型配置中的调度器设置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查并修复 scheduler_cfg
            if 'scheduler_cfg' in config and isinstance(config['scheduler_cfg'], dict):
                if '_partial_' in config['scheduler_cfg']:
                    print(f"  移除 scheduler_cfg._partial_ 从 {file_path}")
                    del config['scheduler_cfg']['_partial_']
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            print(f"✅ 修复完成: {file_path}")
            
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")
    
    def create_fixed_segmentation_module(self):
        """创建修复后的 SegmentationModule"""
        source_file = self.project_root / "src/models/segmentation_module.py"
        fixed_file = self.project_root / "analysis_workspace/fix_implementations/segmentation_module_fixed.py"
        
        # 备份原文件
        self.backup_file(source_file)
        
        # 创建修复版本
        fixed_content = '''import torch
import torch.nn.functional as F
import lightning.pytorch as pl
import hydra
from torchmetrics.classification import JaccardIndex
from torchmetrics.segmentation import DiceScore
from typing import Any, Dict, List, Optional, Union
from omegaconf import DictConfig
import logging

# 导入模型架构
from .architectures import AVAILABLE_ARCHITECTURES

class SegmentationModule(pl.LightningModule):
    """
    语义分割Lightning模块 - 修复版本
    
    修复内容:
    - 解决调度器 _partial_ 兼容性问题
    - 改进 configure_optimizers 方法
    - 增强错误处理和类型安全
    """
    
    def __init__(
        self,
        model_name: str,
        model_params: DictConfig,
        optimizer_cfg: DictConfig,
        scheduler_cfg: Optional[DictConfig],
        loss_cfg: DictConfig,
        num_classes: int,
        ignore_index: int = 255,
        **kwargs
    ):
        super().__init__()
        
        # 保存超参数（忽略已实例化的模块）
        self.save_hyperparameters(ignore=['loss_cfg'])
        
        self.model_name = model_name
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.logger_instance = logging.getLogger(self.__class__.__name__)
        
        # 创建模型架构
        self.architecture = self._create_model(model_name, model_params, num_classes)
        
        # 保存配置，在configure_optimizers中实例化
        self.optimizer_cfg = optimizer_cfg
        self.scheduler_cfg = scheduler_cfg
        
        # 创建损失函数
        if isinstance(loss_cfg, dict) and '_target_' in loss_cfg:
            if loss_cfg['_target_'] == 'torch.nn.CrossEntropyLoss':
                import torch.nn as nn
                self.loss_fn = nn.CrossEntropyLoss(ignore_index=loss_cfg.get('ignore_index', 255))
            else:
                self.loss_fn = hydra.utils.instantiate(loss_cfg)
        else:
            # 默认使用CrossEntropyLoss
            import torch.nn as nn
            self.loss_fn = nn.CrossEntropyLoss(ignore_index=self.ignore_index)
        
        # 创建指标
        self.train_iou = JaccardIndex(
            num_classes=self.num_classes,
            task='multiclass',
            ignore_index=self.ignore_index
        )
        self.val_iou = JaccardIndex(
            num_classes=self.num_classes,
            task='multiclass',
            ignore_index=self.ignore_index
        )
        self.train_dice = DiceScore(num_classes=self.num_classes)
        self.val_dice = DiceScore(num_classes=self.num_classes)
        
        # 记录模型信息
        self._log_model_info()
    
    def _create_model(self, model_name: str, model_params: DictConfig, num_classes: int):
        """创建模型架构"""
        if model_name not in AVAILABLE_ARCHITECTURES:
            raise ValueError(
                f"不支持的模型架构: {model_name}。"
                f"可用的架构: {list(AVAILABLE_ARCHITECTURES.keys())}"
            )
        
        model_class = AVAILABLE_ARCHITECTURES[model_name]
        
        # 准备模型参数
        model_kwargs = dict(model_params)
        model_kwargs['num_classes'] = num_classes
        
        # 创建模型
        model = model_class(**model_kwargs)
        
        self.logger_instance.info(f"创建模型: {model_name}")
        self.logger_instance.info(f"模型参数: {model_kwargs}")
        
        return model
    
    def _log_model_info(self):
        """记录模型信息"""
        if hasattr(self.architecture, 'get_model_info'):
            model_info = self.architecture.get_model_info()
            self.logger_instance.info("="*50)
            self.logger_instance.info("模型详细信息:")
            for key, value in model_info.items():
                self.logger_instance.info(f"{key}: {value}")
            self.logger_instance.info("="*50)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.architecture(x)
    
    def _shared_step(self, batch: Dict[str, torch.Tensor], batch_idx: int, stage: str):
        """共享的训练/验证步骤"""
        images = batch["image"]
        masks = batch["mask"]
        
        # 前向传播
        logits = self.forward(images)
        
        # 计算损失
        loss = self.loss_fn(logits, masks)
        
        # 获取预测结果
        preds = torch.argmax(logits, dim=1)
        
        # 选择对应的指标
        iou_metric = self.train_iou if stage == "train" else self.val_iou
        dice_metric = self.train_dice if stage == "train" else self.val_dice
        
        # 更新指标
        iou_metric(preds, masks)
        dice_metric(preds, masks)
        
        # 记录指标
        self.log(f"{stage}/loss", loss, on_step=True, on_epoch=True, prog_bar=True, sync_dist=True)
        self.log(f"{stage}/iou", iou_metric, on_step=False, on_epoch=True, prog_bar=True, sync_dist=True)
        self.log(f"{stage}/dice", dice_metric, on_step=False, on_epoch=True, prog_bar=True, sync_dist=True)
        
        # 记录学习率（仅训练时）
        if stage == "train":
            try:
                if hasattr(self, '_trainer') and self._trainer is not None:
                    self.log("lr", self.optimizers().param_groups[0]['lr'], on_step=True, on_epoch=False, prog_bar=True)
            except:
                pass
        
        return loss
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        return self._shared_step(batch, batch_idx, "train")
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        self._shared_step(batch, batch_idx, "val")
    
    def configure_optimizers(self) -> Union[torch.optim.Optimizer, Dict[str, Any]]:
        """
        配置优化器和学习率调度器 - 修复版本
        
        修复内容:
        - 移除对 _partial_ 的依赖
        - 确保返回实例化的调度器对象
        - 改进错误处理
        """
        # 1. 创建优化器
        try:
            # 直接实例化优化器，不使用 _partial_
            optimizer = hydra.utils.instantiate(
                self.optimizer_cfg, 
                params=self.architecture.parameters(),
                _partial_=False  # 强制不使用 partial
            )
            self.logger_instance.info(f"优化器创建成功: {type(optimizer).__name__}")
        except Exception as e:
            # 降级到默认优化器
            import torch.optim as optim
            optimizer = optim.Adam(self.architecture.parameters(), lr=0.001)
            self.logger_instance.warning(f"优化器配置失败，使用默认Adam: {e}")
        
        # 2. 如果没有调度器配置，只返回优化器
        if self.scheduler_cfg is None:
            return optimizer
        
        # 3. 创建调度器
        try:
            # 直接实例化调度器，传入优化器参数
            scheduler = hydra.utils.instantiate(
                self.scheduler_cfg,
                optimizer=optimizer,
                _partial_=False  # 强制不使用 partial
            )
            self.logger_instance.info(f"调度器创建成功: {type(scheduler).__name__}")
            
            # 验证调度器对象
            if not hasattr(scheduler, 'optimizer'):
                raise ValueError("调度器对象缺少 optimizer 属性")
            
        except Exception as e:
            # 降级到默认调度器
            import torch.optim.lr_scheduler as lr_scheduler
            scheduler = lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)
            self.logger_instance.warning(f"调度器配置失败，使用默认StepLR: {e}")
        
        # 4. 返回配置字典
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val/loss",
                "interval": "epoch",
                "frequency": 1,
            },
        }
    
    def on_validation_epoch_end(self) -> None:
        """验证epoch结束时的处理"""
        # 计算并记录最终的验证指标
        val_iou_value = self.val_iou.compute()
        val_dice_value = self.val_dice.compute()
        
        self.log("val/iou_final", val_iou_value, on_epoch=True, sync_dist=True)
        self.log("val/dice_final", val_dice_value, on_epoch=True, sync_dist=True)
        
        # 重置指标状态
        self.val_iou.reset()
        self.val_dice.reset()
    
    def test_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        """测试步骤"""
        return self._shared_step(batch, batch_idx, "test")
    
    def predict_step(self, batch: Dict[str, torch.Tensor], batch_idx: int, dataloader_idx: int = 0):
        """预测步骤"""
        images = batch["image"]
        logits = self.forward(images)
        preds = torch.argmax(logits, dim=1)
        return {
            "predictions": preds,
            "logits": logits,
            "image_names": batch.get("image_name", None)
        }
'''
        
        with open(fixed_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ 创建修复版本: {fixed_file}")
        return fixed_file

def main():
    """主修复函数"""
    print("=" * 80)
    print("DL_Lightning 调度器兼容性修复")
    print("=" * 80)
    
    fixer = SchedulerCompatibilityFixer(".")
    
    print("\n1. 修复调度器配置文件...")
    fixer.fix_scheduler_configs()
    
    print("\n2. 修复模型配置文件...")
    fixer.fix_model_configs()
    
    print("\n3. 创建修复版本的 SegmentationModule...")
    fixed_module = fixer.create_fixed_segmentation_module()
    
    print(f"\n✅ 修复完成！")
    print(f"📁 备份文件位置: {fixer.backup_dir}")
    print(f"📄 修复版本模块: {fixed_module}")
    print(f"\n下一步: 运行测试验证修复效果")

if __name__ == "__main__":
    main()
