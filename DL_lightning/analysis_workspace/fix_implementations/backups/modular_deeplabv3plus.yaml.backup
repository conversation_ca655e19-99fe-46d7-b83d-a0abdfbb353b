# 模块化自适应DeepLabV3+ 模型配置文件 - Lightning版本
# 适配PyTorch Lightning + Hydra配置系统

_target_: src.models.segmentation_module.SegmentationModule

# 模型架构配置
model_name: modular_deeplabv3plus
model_params:
  backbone: resnet50           # 骨干网络: resnet50, resnet101
  in_channels: 3               # 输入通道数
  pretrained: true             # 是否使用预训练权重
  
  # 插件配置 - 模块化特征增强
  plugins_config:
    # 通道注意力插件
    channel_attention:
      enabled: true             # 是否启用通道注意力
      reduction: 16             # 通道压缩比例
    
    # 空间注意力插件  
    spatial_attention:
      enabled: true             # 是否启用空间注意力
      kernel_size: 7            # 卷积核大小
    
    # 金字塔池化插件
    pyramid_pooling:
      enabled: true             # 是否启用金字塔池化
      pool_sizes: [1, 2, 3, 6]  # 池化尺寸列表

# 训练配置
num_classes: ${data.num_classes}    # 分割类别数
ignore_index: 255                  # 忽略的标签索引

# 优化器配置（模块化模型通常需要较小的学习率）
optimizer_cfg:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 0.0005                   # 较小的学习率
  weight_decay: 0.01
  betas: [0.9, 0.999]

# 学习率调度器配置
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  _partial_: true
  T_0: 10                      # 重启周期
  T_mult: 2                    # 周期倍增因子
  eta_min: 1e-7                # 最小学习率

# 损失函数配置（从loss配置组引用）
loss_cfg: ${loss}

# 模型名称（用于日志记录）
name: "ModularAdaptiveDeepLabV3Plus"
