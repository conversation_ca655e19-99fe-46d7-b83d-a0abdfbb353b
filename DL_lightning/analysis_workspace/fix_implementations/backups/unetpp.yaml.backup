# UNet++ 模型配置文件 - Lightning版本
# 适配PyTorch Lightning + Hydra配置系统

_target_: src.models.segmentation_module.SegmentationModule

# 模型架构配置
model_name: unetpp
model_params:
  in_channels: 3                    # 输入通道数
  features: [64, 128, 256, 512]     # 各层特征通道数
  bilinear: true                    # 是否使用双线性插值上采样
  deep_supervision: false           # 是否使用深度监督
  pretrained: false                 # UNet++通常不使用预训练权重

# 训练配置
num_classes: ${data.num_classes}    # 分割类别数
ignore_index: 255                  # 忽略的标签索引

# 优化器配置
optimizer_cfg:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 0.001
  weight_decay: 0.01
  betas: [0.9, 0.999]

# 学习率调度器配置
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  _partial_: true
  T_0: 10
  T_mult: 2
  eta_min: 1e-6

# 损失函数配置（从loss配置组引用）
loss_cfg: ${loss}

# 模型名称（用于日志记录）
name: "UNet++"
