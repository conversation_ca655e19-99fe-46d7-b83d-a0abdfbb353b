# Swin-UNet 模型配置文件 - Lightning版本
# 适配PyTorch Lightning + Hydra配置系统

_target_: src.models.segmentation_module.SegmentationModule

# 模型架构配置
model_name: swin_unet
model_params:
  img_size: 512                     # 输入图像尺寸
  in_channels: 3                    # 输入通道数
  depths: [2, 2, 6, 2]             # 各层深度
  pretrained: true                  # 是否使用预训练权重

# 训练配置
num_classes: ${data.num_classes}    # 分割类别数
ignore_index: 255                  # 忽略的标签索引

# 优化器配置（Transformer模型通常需要较小的学习率）
optimizer_cfg:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 0.0001
  weight_decay: 0.05
  betas: [0.9, 0.999]

# 学习率调度器配置
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  _partial_: true
  T_max: 150
  eta_min: 1e-7

# 损失函数配置（从loss配置组引用）
loss_cfg: ${loss}

# 模型名称（用于日志记录）
name: "Swin-UNet"
