# 数据模块架构 - 清理后版本

## 📁 目录结构

```
src/data/
├── datamodules/                    # 方案A分层抽象架构
│   ├── __init__.py                # 导出DataModule类
│   ├── base.py                    # 基础抽象类
│   ├── suide.py                   # SuiDe数据集管理器
│   └── loveda.py                  # LoveDA数据集管理器
├── datasets/                      # 具体数据集实现
│   ├── __init__.py               # 导出Dataset类
│   ├── suide_dataset.py          # SuiDe数据读取器 ⭐推荐
│   └── loveda.py                 # LoveDA数据读取器
├── transforms_lightning.py       # 数据增强工具
├── __init__.py                   # 统一导出接口
└── remote_sensing_datamodule.py  # 向后兼容包装器
```

## 🏗️ 架构层次

### 第1层：配置文件
```yaml
configs/data/
├── suide_v2.1_new.yaml          # SuiDe配置
└── loveda.yaml                   # LoveDA配置
```

### 第2层：DataModule（数据管理器）
```python
BaseRemoteSensingDataModule      # 抽象基类
├── SuiDeDataModule             # SuiDe专用管理器
└── LoveDADataModule            # LoveDA专用管理器
```

### 第3层：Dataset（数据读取器）
```python
SuiDeDatasetV2                  # SuiDe数据读取
LoveDADataset                   # LoveDA数据读取
```

## 🔄 数据流转

```
配置文件 → DataModule → Dataset → 数据文件
   ↓         ↓          ↓         ↓
 参数配置   管理逻辑    读取逻辑   原始数据
```

## 💻 使用方式

### 推荐方式（配置文件）
```python
import hydra
from omegaconf import OmegaConf

# 加载配置并创建DataModule
config = OmegaConf.load('configs/data/suide_v2.1_new.yaml')
datamodule = hydra.utils.instantiate(config)

# 使用
datamodule.setup('fit')
train_loader = datamodule.train_dataloader()
```

### 直接创建方式
```python
from src.data.datamodules import SuiDeDataModule
from omegaconf import DictConfig

# 创建DataModule
datamodule = SuiDeDataModule(
    dataset_config=DictConfig({...}),
    dataloader_config=DictConfig({...}),
    transform_config=DictConfig({...})
)
```

## 🎯 核心文件说明

| 文件 | 作用 | 重要程度 |
|------|------|----------|
| `datamodules/base.py` | 所有DataModule的父类 | ⭐⭐⭐⭐⭐ |
| `datamodules/suide.py` | SuiDe数据集管理器 | ⭐⭐⭐⭐⭐ |
| `datamodules/loveda.py` | LoveDA数据集管理器 | ⭐⭐⭐⭐⭐ |
| `datasets/suide_dataset.py` | SuiDe数据读取器 | ⭐⭐⭐⭐⭐ |
| `datasets/loveda.py` | LoveDA数据读取器 | ⭐⭐⭐⭐⭐ |
| `transforms_lightning.py` | 数据增强工具 | ⭐⭐⭐⭐ |

## ✅ 清理完成

- ❌ 删除了 `datasets/suide.py` (旧版本)
- ❌ 删除了 `datasets/suid_v2.py` (旧版本)
- ✅ 保留了 `datasets/suide_dataset.py` (推荐版本)
- ✅ 更新了 `datasets/__init__.py` 导入

现在代码结构更清晰，没有冗余的旧版本文件干扰阅读！
