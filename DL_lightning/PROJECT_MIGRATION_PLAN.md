# DL_Lightning 现代化深度学习框架
**基于 Lightning + WandB + Ray Tune 的黄金架构重构**

## 📋 项目概述

### 迁移背景
- **源项目**: `DL_Framework` - 自研训练框架，基于配置驱动
- **目标**: 构建现代化、自动化的科研工作流平台
- **重构目标**: 在 `DL_lightning` 目录中实现新架构

### 当前状态分析
- **核心组件**: 
  - `src/training/trainer.py` - 自定义训练器
  - `src/models/factory.py` - 模型工厂
  - `src/data/factory.py` - 数据加载工厂
  - `src/training/callbacks` - 回调系统
- **启动方式**: `./train.sh configs/config.yaml`
- **实验管理**: 基于文件系统的 `ExperimentManager`

### 目标架构
- **执行引擎**: Lightning (PyTorch Lightning) - 专业的训练抽象层
- **实验跟踪**: Weights & Biases (WandB) - 可视化和协作平台
- **超参优化**: Ray Tune - 智能超参数搜索和调度
- **工作流**: 完全自动化的科研流水线

### 预期收益（专用于遥感图像语义分割）
1. **分割精度提升**: 专业的损失函数组合(CE + Dice Loss)，针对遥感数据优化
2. **训练效率提升 300%**: Lightning自动优化 + Ray Tune智能调度，专门针对分割任务
3. **分割指标可视化**: WandB自动记录IoU、Dice、分割mask可视化和类别统计
4. **模型对比分析**: 快速对比UNet、DeepLabV3+、SegFormer在遥感数据上的性能
5. **遥感数据处理**: 专门的多光谱/高分辨率图像预处理和数据增强策略

---

技术栈： Hydra + Lightning + WandB + Ray Tune

## 🏗️ DL_Lightning 项目结构

### 精简目录架构（专用于遥感图像语义分割）
```
DL_lightning/
├── PROJECT_MIGRATION_PLAN.md    # 本文档
├── README.md                     # 项目说明
├── requirements.txt              # 依赖包列表
│
├── src/                          # 核心源码
│   ├── __init__.py
│   ├── models/                   # 语义分割模型
│   │   ├── __init__.py
│   │   ├── segmentation_module.py   # 语义分割LightningModule
│   │   └── architectures/           # 分割模型实现
│   │       ├── __init__.py
│   │       ├── unet.py             # UNet系列
│   │       ├── deeplabv3.py        # DeepLabV3+
│   │       └── segformer.py        # SegFormer
│   │
│   ├── data/                     # 遥感数据模块
│   │   ├── __init__.py
│   │   ├── remote_sensing_datamodule.py  # 遥感数据DataModule
│   │   ├── transforms.py                # 遥感数据增强
│   │   └── datasets/                     # 遥感数据集
│   │       ├── __init__.py
│   │       └── remote_sensing_dataset.py
│   │
│   ├── losses/                   # 自定义损失函数
│   │   ├── __init__.py
│   │   └── dice_loss.py
│   │
│   ├── optimizers/               # 自定义优化器 (如果需要)
│   │   └── __init__.py
│   │
│   ├── schedulers/               # 自定义学习率调度器 (如果需要)
│   │   └── __init__.py
│   │
│   ├── callbacks/                # Lightning回调
│   │   ├── __init__.py
│   │   ├── wandb_callbacks.py    # WandB集成
│   │   └── tune_callbacks.py     # Ray Tune集成
│   │
│   └── utils/                    # 工具函数
│       ├── __init__.py
│       ├── hydra_resolvers.py    # 注册自定义Hydra解析器
│       └── metrics.py            # 分割指标(IoU, Dice等)
│
├── scripts/                      # 执行脚本
│   ├── train.py                  # 单次训练入口
│   ├── tune.py                   # HPO实验入口
│   └── predict.py                # 预测脚本
│
├── configs/                      # Hydra配置
│   ├── config.yaml              # 主配置文件，定义默认组合
│   ├── logger/
│   │   └── wandb.yaml
│   ├── callbacks/
│   │   └── default.yaml
│   ├── model/                    # 模型配置
│   │   ├── unet.yaml
│   │   ├── deeplabv3.yaml
│   │   └── segformer.yaml
│   ├── data/                     # 数据配置
│   │   └── remote_sensing.yaml
│   ├── loss/
│   │   ├── ce_dice.yaml
│   │   └── focal.yaml
│   ├── optimizer/                # 优化器配置
│   │   ├── adamw.yaml
│   │   └── sgd.yaml
│   ├── scheduler/                # 学习率调度器配置
│   │   ├── cosine.yaml
│   │   └── poly.yaml
│   ├── trainer/                  # Trainer配置
│   │   └── default.yaml
│   └── experiment/               # 实验配置 (覆盖默认值)
│       ├── baseline_unet.yaml
│       └── hpo_search_space.yaml
│
└── experiments_outputs/          # 所有实验的根输出目录
    └── YYYY-MM-DD-SS/            # 运行日期, 每次运行的唯一ID目录 (由Hydra创建)
        ├── .hydra/               # Hydra配置快照 (保证可复现)
        │   ├── config.yaml
        │   └── overrides.yaml
        │
        ├── checkpoints/          # 1. 模型检查点
        │   └── last.ckpt
        │
        ├── predictions/          # 2. 预测结果图片
        │   └── test_img_pred.png
        │
        ├── wandb/                # 3. WandB 日志 (强制指定到此目录)
        │   └── run-XYZ-media...
        │
        ├── ray_info/             # 4. Ray Tune 试验结果 (如果使用)
        │   └── result.json
        │
        └── main.log              # 5. Python运行日志

---

## 🎯 四阶段实施计划 (方案E: Hydra原生扩展)

### 阶段一：核心骨架与单次训练 (第1-2周)
**目标**: 搭建基于Hydra原生扩展的项目骨架，跑通一次完整的训练流程。

#### 1.1 环境与脚手架 (1天)
- **任务**:
  - [ ] 根据最终确定的结构，创建所有目录和空的 `__init__.py` 文件。
  - [ ] 根据 `requirements.txt` 安装所有依赖。

**命令**:
```bash
# (在 DL_lightning 父目录中)
mkdir -p DL_lightning/{src,scripts,configs,experiments_outputs}
# ... (根据结构图创建所有子目录) ...
cd DL_lightning
# 激活已有的 'SuiDe' Conda 环境
conda activate SuiDe
# 安装更新后的依赖
pip install -r requirements.txt
wandb login
```

#### 1.2 Hydra原生扩展配置 (3天)
- **任务**:
  - [ ] 实现 `src/utils/hydra_resolvers.py`，注册 `get_run_dir` 等核心解析器。
  - [ ] 创建基础YAML配置:
    - `configs/config.yaml` (定义默认组合)
    - `configs/trainer/default.yaml`
    - `configs/logger/wandb.yaml` (使用 `${get_run_dir:}` )
    - `configs/callbacks/default.yaml` (使用 `${path_join:${get_run_dir:},"checkpoints"}` )

#### 1.3 核心模块实现 (4-5天)
- **任务**:
  - [ ] 实现 `src/data/remote_sensing_datamodule.py` 和相关的 `dataset` / `transforms`。
  - [ ] 实现 `src/models/segmentation_module.py`，其 `configure_optimizers` 方法需使用 `hydra.utils.instantiate` 来动态创建优化器和调度器。
  - [ ] 实现 `src/models/architectures/unet.py` 作为初始模型。
  - [ ] 创建对应的模型、数据、优化器、调度器配置文件。

#### 1.4 单次训练脚本 (2天)
- **任务**:
  - [ ] 实现 `scripts/train.py`。
  - [ ] 在脚本开头调用 `register_hydra_resolvers()`。
  - [ ] `main` 函数使用 `hydra.utils.instantiate` 来创建 datamodule, model, logger, callbacks 和 trainer。
- **里程碑验证**:
  ```bash
  python scripts/train.py experiment=baseline_unet
  ```
  ✅ 训练成功运行。
  ✅ 在 `experiments_outputs/` 下创建了唯一的运行目录，且 `checkpoints` 和 `wandb` 日志都保存在其中。

---

### 阶段二：WandB深度可视化 (第3周)
**目标**: 充分利用WandB的可视化能力，跟踪分割任务的关键信息。

#### 2.1 基础指标和配置跟踪 (1天)
- **任务**:
  - [ ] 确保 `WandbLogger` 自动记录所有 `self.log()` 的指标。
  - [ ] 确保 `hydra.utils.instantiate` 创建的所有对象的超参数都被完整记录到WandB的config中。

#### 2.2 自定义分割结果可视化 (3-4天)
- **文件**: `src/callbacks/wandb_callbacks.py`
- **任务**:
  - [ ] 创建一个 `LogSegmentationMasksCallback` 的自定义回调。
  - [ ] 在 `on_validation_epoch_end` 钩子中，从验证集中随机抽取N个样本。
  - [ ] 将样本的 `原图 (Image)`, `真实标签 (Ground Truth)` 和 `模型预测 (Prediction)` 合成一张图，使用 `wandb.Image` 上传。
  - [ ] 在 `configs/callbacks/default.yaml` 中添加这个新回调的配置。

- **里程碑验证**:
  - [ ] WandB的运行页面中，除了指标曲线外，还能看到每个epoch结束时上传的分割结果对比图。

---

### 阶段三：Ray Tune 超参优化 (第4-5周)
**目标**: 实现基于Hydra配置的、自动化的超参数搜索。

#### 3.1 HPO脚本实现 (3天)
- **文件**: `scripts/tune.py`
- **任务**:
  - [ ] `tune.py` 脚本使用Hydra加载基础配置 (`experiment=...`) 和搜索空间配置 (`hpo_search_space.yaml`)。
  - [ ] 定义一个 `objective` 函数，该函数接收Ray Tune的 `trial` 配置，并将其转换为Hydra的覆盖参数 (`overrides`)。
  - [ ] 在 `objective` 函数内部，调用 `train.py` 的 `main` 函数来执行一次训练。
  - [ ] 使用 `RayTuneReportCallback` 将指标报告给Ray Tune。

#### 3.2 调度与结果分析 (2天)
- **任务**:
  - [ ] 在 `tune.py` 中配置ASHA调度器。
  - [ ] 确保每次HPO的子试验，其WandB日志能被正确地分组到一个统一的group下。
  - [ ] HPO结束后，自动打印出最佳的超参数组合。

- **里程碑验证**:
  ```bash
  python scripts/tune.py experiment=baseline_unet
  ```
  ✅ HPO成功运行，ASHA能提前终止劣质试验。
  ✅ WandB中出现一个实验组，包含了本次HPO的所有子试验。

---

### 阶段四：预测、测试与文档 (第6周)
**目标**: 完善工作流，确保框架的可用性和健壮性。

#### 4.1 推理脚本 (2天)
- **文件**: `scripts/predict.py`
- **任务**:
  - [ ] `predict.py` 接收一个训练好的模型checkpoint路径 (`.ckpt`) 作为输入。
  - [ ] 脚本会自动加载模型和相关的配置。
  - [ ] 对指定的输入图像/文件夹进行分割预测，并保存结果。

#### 4.2 测试和文档 (3天)
- **任务**:
  - [ ] 编写核心功能的单元测试（例如 `metrics.py`）。
  - [ ] 编写`README.md`，包含项目介绍、安装指南和快速开始命令。
  - [ ] 录制一个简短的视频教程，演示从训练到优化的全过程。

- **最终里程碑**:
  - [ ] 项目达到可交付状态，新成员能在30分钟内根据 `README.md` 跑通第一个实验。

---

## 🛠️ 关键技术实现

### 核心依赖管理
**文件**: `requirements.txt`
```txt
# === 核心依赖 Core Dependencies ===

# --- 深度学习框架 (Deep Learning Framework) ---
# PyTorch: 核心计算库
# Torchvision: 视觉处理库
torch>=2.3.1
torchvision>=0.18.1

# --- 训练引擎 (Training Engine) ---
# Lightning: (原Pytorch-Lightning) 高级训练框架，简化代码
lightning>=2.3.0

# --- 实验跟踪与超参优化 (Experiment Tracking & HPO) ---
# Weights & Biases: 用于记录、可视化和比较实验结果
wandb>=0.17.0
# Ray Tune: 分布式超参数搜索引擎
ray[tune]>=2.10.0
# Ray Lightning: Ray与Lightning的集成插件
ray_lightning>=0.2.0
# Optuna: 备选的超参数优化框架
optuna>=3.6.0

# --- 配置管理 (Configuration Management) ---
# Hydra: 强大且灵活的应用配置工具
hydra-core>=1.3.2
# OmegaConf: Hydra使用的YAML/Dict配置解析库
omegaconf>=2.3.0
# Jsonargparse: Lightning依赖，用于命令行参数解析
jsonargparse[signatures]>=4.27.0

# === 数据处理与工具库 Data Processing & Utilities ===

# --- 数据增强与图像处理 (Data Augmentation & Image Processing) ---
albumentations>=1.4.0
opencv-python>=4.9.0
pillow>=10.3.0

# --- 常用工具 (Common Utilities) ---
rich>=13.7.0          # 终端富文本输出
tqdm>=4.66.0          # 进度条
matplotlib>=3.9.0     # 绘图
seaborn>=0.13.0       # 统计可视化
pandas>=2.2.0         # 数据分析
numpy>=1.26.0         # 科学计算

# === 开发与代码质量 (Development & Code Quality) ===
pytest>=8.2.0         # 测试框架
black>=24.4.0         # 代码格式化
isort>=5.13.0         # import排序
mypy>=1.10.0          # 静态类型检查
```

### Hydra 原生扩展 (方案E)
**核心思想**: 不编写复杂的管理代码，而是将管理逻辑“融入”到配置文件中。通过注册自定义的Hydra解析器，让YAML文件本身具备动态计算路径和参数的能力。

**1. 自定义解析器注册**
**文件**: `src/utils/hydra_resolvers.py`
```python
import os
import hydra
from omegaconf import OmegaConf

def register_hydra_resolvers() -> None:
    """在程序启动时，一次性注册所有自定义的Hydra功能。"""
    
    # 注册一个函数，用于获取当前Hydra运行的输出目录
    # ${get_run_dir:} 将返回一个唯一的路径，如: outputs/YYYY-MM-DD/HH-MM-SS
    OmegaConf.register_new_resolver(
        "get_run_dir",
        lambda: hydra.core.hydra_config.HydraConfig.get().runtime.output_dir
    )

    # 注册一个函数，用于安全地拼接路径
    OmegaConf.register_new_resolver(
        "path_join",
        lambda *args: os.path.join(*args)
    )

    # 更多... 例如获取git commit hash等
```

**2. 配置文件调用解析器**
**文件**: `configs/callbacks/default.yaml`
```yaml
model_checkpoint:
  _target_: lightning.pytorch.callbacks.ModelCheckpoint
  
  # dirpath不再是写死的，而是动态计算出来的！
  # 它会调用 os.path.join(${get_run_dir:}, "checkpoints")
  dirpath: ${path_join:${get_run_dir:}, "checkpoints"}
  
  filename: "{epoch}-{step}-best"
  monitor: "val/iou"
  mode: "max"
  save_top_k: 1
```

**文件**: `configs/logger/wandb.yaml`
```yaml
_target_: lightning.pytorch.loggers.WandbLogger

# save_dir 也将指向那个唯一的运行目录
save_dir: ${get_run_dir:}

project: ${wandb.project} # 从 wandb/default.yaml 中引用项目名
name: ${experiment.name}-${now:%Y-%m-%d_%H-%M-%S}
```

### 遥感语义分割模块
**文件**: `src/models/segmentation_module.py`
```python
import torch
import torch.nn.functional as F
import lightning.pytorch as pl
import hydra
from torchmetrics import JaccardIndex, Dice
from typing import Any, Dict, Tuple

class SegmentationModule(pl.LightningModule):
    def __init__(
        self,
        model: torch.nn.Module,
        num_classes: int,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-4,
        class_weights: torch.Tensor = None,
        **kwargs
    ):
        super().__init__()
        self.save_hyperparameters(ignore=['model'])
        self.model = model
        
        # 使用 torchmetrics 计算指标
        self.train_iou = JaccardIndex(num_classes=num_classes, task='multiclass')
        self.val_iou = JaccardIndex(num_classes=num_classes, task='multiclass')
        self.train_dice = Dice(num_classes=num_classes, task='multiclass')
        self.val_dice = Dice(num_classes=num_classes, task='multiclass')
        
        # 类别权重用于处理不平衡数据
        self.register_buffer('class_weights', class_weights)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)
    
    def compute_loss(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """计算分割损失 (CrossEntropy + Dice)"""
        ce_loss = F.cross_entropy(predictions, targets, weight=self.class_weights)
        
        # Dice Loss for better boundary segmentation
        predictions_soft = F.softmax(predictions, dim=1)
        dice_loss = 1 - self.dice_coefficient(predictions_soft, targets)
        
        return ce_loss + 0.5 * dice_loss
    
    def dice_coefficient(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """计算Dice系数"""
        targets_one_hot = F.one_hot(targets, num_classes=self.hparams.num_classes).permute(0, 3, 1, 2)
        intersection = (predictions * targets_one_hot).sum(dim=(2, 3))
        union = predictions.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3))
        dice = (2. * intersection + 1e-8) / (union + 1e-8)
        return dice.mean()
    
    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        images, masks = batch['image'], batch['mask']
        predictions = self(images)
        loss = self.compute_loss(predictions, masks)
        
        # 计算训练指标
        preds = torch.argmax(predictions, dim=1)
        train_iou = self.train_iou(preds, masks)
        train_dice = self.train_dice(preds, masks)
        
        # 记录指标
        self.log_dict({
            'train_loss': loss,
            'train_iou': train_iou,
            'train_dice': train_dice,
            'lr': self.trainer.optimizers[0].param_groups[0]['lr']
        }, prog_bar=True, on_step=True, on_epoch=True)
        
        return loss
    
    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int) -> torch.Tensor:
        images, masks = batch['image'], batch['mask']
        predictions = self(images)
        loss = self.compute_loss(predictions, masks)
        
        # 计算验证指标
        preds = torch.argmax(predictions, dim=1)
        val_iou = self.val_iou(preds, masks)
        val_dice = self.val_dice(preds, masks)
        
        # 记录验证指标
        self.log_dict({
            'val_loss': loss,
            'val_iou': val_iou,
            'val_dice': val_dice
        }, prog_bar=True, sync_dist=True)
        
        return loss
    
    def configure_optimizers(self):
        # 这里的 self.hparams 就是从 Hydra 加载的配置
        optimizer_cfg = self.hparams.optimizer
        scheduler_cfg = self.hparams.scheduler

        # 使用 Hydra 的 instantiate 功能直接创建优化器和调度器
        # 这样，我们就可以在YAML中灵活地切换 Adam, SGD 等
        optimizer = hydra.utils.instantiate(optimizer_cfg, params=self.parameters())
        
        scheduler = hydra.utils.instantiate(scheduler_cfg, optimizer=optimizer)
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "epoch", # 或者从配置中读取
                "monitor": "val/loss"
            }
        }
```

---

## 📊 项目管理和时间线

### 详细时间规划
```
周次 | 阶段 | 主要任务 | 交付物
-----|------|----------|--------
第1周 | Lightning基础 | 环境搭建, 基础组件开发 | 可运行的Lightning训练脚本
第2周 | Lightning完善 | 模型和数据模块完善 | 完整的单次训练功能
第3周 | WandB集成 | 实验跟踪和可视化 | 可视化仪表盘和指标记录
第4周 | Ray Tune基础 | HPO基础架构 | 基本的超参数搜索功能
第5周 | Ray Tune高级 | 智能调度和大规模实验 | 生产级HPO平台
第6周 | 优化和文档 | 工具完善, 文档编写 | 完整的文档和示例
```

### 关键里程碑
1. **M1 (第2周末)**: Lightning单次训练功能完整
2. **M2 (第3周末)**: WandB可视化和跟踪就绪
3. **M3 (第5周末)**: Ray Tune自动化HPO平台就绪
4. **M4 (第6周末)**: 生产环境部署就绪

### 风险缓解策略
1. **技术风险**: 每周进行技术review，及时解决阻塞问题
2. **性能风险**: 持续进行性能基准测试
3. **兼容性风险**: 保持与原框架的API兼容性
4. **学习成本**: 提供充分的文档和示例

---

## ✅ 验收标准

### 功能性验收
- [ ] **单次训练**: 30秒内启动，结果可复现
- [ ] **超参优化**: 支持100+并发试验，智能剪枝
- [ ] **实验跟踪**: 自动记录所有实验数据
- [ ] **可扩展性**: 支持单机到集群的扩展
- [ ] **易用性**: 新用户15分钟内完成首次训练

### 性能验收
- [ ] 训练性能≥原框架的98%
- [ ] HPO效率提升≥300% (通过智能剪枝)
- [ ] 资源利用率提升≥200%
- [ ] 实验启动时间<30秒

### 质量验收
- [ ] 代码覆盖率≥80%
- [ ] 文档覆盖率100%
- [ ] 零已知关键bug
- [ ] 通过所有CI/CD检查

---

## 🚀 快速开始

### 安装和设置
```bash
# 1. 克隆项目
git clone <repository-url> DL_lightning
cd DL_lightning

# 2. 激活 Conda 环境
# 确保您已创建名为 'SuiDe' 的 Conda 环境
conda activate SuiDe

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置WandB
wandb login
```

### 运行单次训练
```bash
# 5. 运行基线实验 (例如使用UNet模型)
python scripts/train.py experiment=baseline_unet
```

### 启动超参数优化
```bash
# 启动HPO实验
python scripts/tune.py experiment=baseline_unet
```

### 查看结果
- **WandB仪表盘**: `https://wandb.ai/<your-entity>/<your-project>`
- **Ray仪表盘**: `http://localhost:8265`

---

**📋 本文档将随项目进展持续更新，确保团队对迁移进度和目标的一致理解。** 