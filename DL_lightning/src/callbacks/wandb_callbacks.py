import torch
import lightning.pytorch as pl
from lightning.pytorch.callbacks import Callback
from lightning.pytorch.loggers import WandbLogger
import wandb
import numpy as np
from PIL import Image
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def get_wandb_logger(trainer: pl.Trainer) -> Optional[WandbLogger]:
    """
    安全地获取WandbLogger

    Args:
        trainer: Lightning Trainer实例

    Returns:
        WandbLogger实例，如果未找到或WandB被禁用则返回None
    """
    for logger_instance in trainer.loggers:
        if isinstance(logger_instance, WandbLogger):
            # 检查WandB是否实际可用
            if hasattr(logger_instance, 'experiment') and logger_instance.experiment:
                return logger_instance
            else:
                logger.warning("WandbLogger存在但experiment不可用，可能处于禁用模式")
                return None

    logger.debug("未找到WandbLogger，可能WandB被禁用或使用其他logger")
    return None

def create_segmentation_image(image: np.ndarray, mask: np.ndarray, prediction: np.ndarray, class_labels: dict) -> Image.Image:
    """
    将原图、真实标签和预测结果合成为一张对比图。
    
    Args:
        image (np.ndarray): (C, H, W), 范围 [0, 1] 的浮点数
        mask (np.ndarray): (H, W), 整数类别
        prediction (np.ndarray): (H, W), 整数类别
        class_labels (dict): 类别ID到名称的映射
        
    Returns:
        PIL.Image.Image: 合成后的图像
    """
    # 将图像从 (C, H, W) 转换为 (H, W, C) 并调整到 [0, 255]
    img_display = (image.transpose(1, 2, 0) * 255).astype(np.uint8)
    
    # 将单通道的mask和prediction转换为3通道的RGB图像
    mask_display = np.stack([mask, mask, mask], axis=-1).astype(np.uint8)
    pred_display = np.stack([prediction, prediction, prediction], axis=-1).astype(np.uint8)

    # 为了更好的可视化，可以给不同的类别赋予不同的颜色
    # 这里为了简单，我们先用灰度图，但这是一个可以扩展的点
    # 例如： palette = [[R,G,B], [R,G,B], ...]
    # mask_display = palette[mask_display]
    
    # 拼接三张图
    combined_image = np.concatenate([img_display, mask_display, pred_display], axis=1)
    
    return Image.fromarray(combined_image)


class LogSegmentationMasksCallback(Callback):
    """
    在每个验证周期结束时，记录N个样本的分割结果到WandB

    支持WandB的不同运行模式：
    - online: 实时上传到云端
    - offline: 保存到本地，可后续同步
    - disabled: 跳过记录
    """

    def __init__(self, num_samples: int = 8, log_frequency: int = 1):
        """
        初始化回调

        Args:
            num_samples: 每次记录的样本数量
            log_frequency: 记录频率（每N个epoch记录一次）
        """
        super().__init__()
        self.num_samples = num_samples
        self.log_frequency = log_frequency
        self.logger = logging.getLogger(self.__class__.__name__)

    def on_validation_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        # 检查是否需要在此epoch记录
        if trainer.current_epoch % self.log_frequency != 0:
            return

        # 0. 安全地获取logger
        wandb_logger = get_wandb_logger(trainer)
        if wandb_logger is None:
            self.logger.debug("WandB不可用，跳过分割结果记录")
            return

        # 1. 从验证数据加载器中获取一个批次
        val_loader = trainer.val_dataloaders
        if not val_loader:
            self.logger.warning("验证数据加载器不可用")
            return
            
        try:
            val_batch = next(iter(val_loader))
        except StopIteration:
            print("Validation Dataloader is empty, skipping mask logging.")
            return

        images, masks = val_batch['image'], val_batch['mask']
        
        # 将数据移动到与模型相同的设备
        images = images.to(pl_module.device)
        masks = masks.to(pl_module.device)

        # 2. 获取模型预测
        pl_module.eval()
        with torch.no_grad():
            logits = pl_module(images)
        pl_module.train()
        
        preds = torch.argmax(logits, dim=1)

        # 3. 记录指定数量的样本
        samples_to_log = min(self.num_samples, len(images))
        
        wandb_images = []
        # 假设我们有一个类别标签字典，如果没有，就创建一个默认的
        class_labels = getattr(trainer.datamodule, 'class_labels', {i: f"Class {i}" for i in range(pl_module.hparams.num_classes)})

        for i in range(samples_to_log):
            # 将tensors转换为numpy数组
            img_np = images[i].cpu().numpy()
            mask_np = masks[i].cpu().numpy()
            pred_np = preds[i].cpu().numpy()
            
            # 创建合成图像
            composite_image = create_segmentation_image(img_np, mask_np, pred_np, class_labels)
            
            # 创建 wandb.Image 对象
            wandb_images.append(wandb.Image(
                composite_image, 
                caption=f"Sample {i} | Epoch {trainer.current_epoch}"
            ))

        # 4. 上传到WandB
        try:
            wandb_logger.experiment.log({
                "val/segmentation_masks": wandb_images,
                "epoch": trainer.current_epoch
            })
            self.logger.info(f"成功记录 {samples_to_log} 个分割样本到WandB (Epoch {trainer.current_epoch})")
        except Exception as e:
            self.logger.error(f"WandB记录失败: {e}")
            # 在离线模式下，这种错误是可以接受的
