#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ray Tune专用控制台输出管理器

功能特性:
- 多进程输出协调
- Ray Tune仪表盘集成
- WandB状态统一管理
- 实时最佳结果更新
"""

import os
import time
import threading
from queue import Queue, Empty
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from rich.console import Console
from rich.live import Live
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.columns import Columns
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.align import Align

import ray
from ray import tune
from ray.tune import Callback


@dataclass
class TrialStatus:
    """试验状态数据结构"""
    trial_id: str
    status: str  # RUNNING/TERMINATED/ERROR
    config: Dict[str, Any]
    metrics: Dict[str, float]
    wandb_mode: str
    wandb_status: str
    last_update: float
    error_msg: Optional[str] = None


class RayTuneConsoleManager:
    """Ray Tune专用控制台管理器"""
    
    def __init__(self, display_mode: str = "dashboard", refresh_rate: float = 2.0):
        """
        初始化Ray Tune控制台管理器

        Args:
            display_mode: 显示模式 (dashboard/simple/quiet)
            refresh_rate: 刷新频率（秒）
        """
        self.display_mode = display_mode
        self.refresh_rate = refresh_rate

        # 检测环境
        self.is_ray_tune_worker = self._detect_ray_tune_worker()
        self.enabled = True and not self.is_ray_tune_worker  # 简化检测

        if self.enabled:
            self.console = Console(force_terminal=True, width=120)
        else:
            self.console = None

        # 试验状态管理
        self.trials: Dict[str, TrialStatus] = {}
        self.best_trial: Optional[TrialStatus] = None
        self.best_metric_value = float('-inf')
        self.best_metric_name = "val/iou"  # 默认关注的指标

        # 输出控制
        self.output_queue = Queue()
        self.live_display = None
        self.update_thread = None
        self.running = False

        # 统计信息
        self.total_trials = 0
        self.completed_trials = 0
        self.failed_trials = 0
        self.start_time = time.time()
    
    def _detect_ray_tune_worker(self) -> bool:
        """检测是否在Ray Tune worker进程中"""
        return (
            os.getenv('RAY_TRIAL_ID') is not None or
            os.getenv('TUNE_TRIAL_ID') is not None or
            hasattr(ray.tune, 'get_trial_id')
        )
    
    def start_dashboard(self):
        """启动仪表盘显示"""
        if self.display_mode == "quiet" or self.is_ray_tune_worker:
            return
        
        self.running = True
        
        # 创建布局
        layout = self._create_dashboard_layout()
        
        # 启动实时显示
        self.live_display = Live(
            layout, 
            console=self.console,
            refresh_per_second=2,
            transient=False
        )
        self.live_display.start()
        
        # 启动更新线程
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        
        # 打印启动信息
        self._print_startup_banner()
    
    def stop_dashboard(self):
        """停止仪表盘显示"""
        self.running = False
        
        if self.live_display:
            self.live_display.stop()
        
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
        
        # 打印最终总结
        self._print_final_summary()
    
    def _create_dashboard_layout(self) -> Layout:
        """创建仪表盘布局"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=8),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=6)
        )
        
        layout["main"].split_row(
            Layout(name="trials", ratio=2),
            Layout(name="best", ratio=1)
        )
        
        return layout
    
    def _update_loop(self):
        """更新循环"""
        while self.running:
            try:
                # 处理队列中的更新
                while not self.output_queue.empty():
                    try:
                        update_data = self.output_queue.get_nowait()
                        self._process_update(update_data)
                    except Empty:
                        break
                
                # 更新显示
                if self.live_display:
                    layout = self.live_display.renderable
                    self._update_dashboard_content(layout)
                
                time.sleep(0.5)
                
            except Exception as e:
                # 静默处理更新错误，避免干扰主流程
                pass
    
    def _update_dashboard_content(self, layout: Layout):
        """更新仪表盘内容"""
        # 更新头部信息
        layout["header"].update(self._create_header_panel())
        
        # 更新试验列表
        layout["trials"].update(self._create_trials_panel())
        
        # 更新最佳结果
        layout["best"].update(self._create_best_panel())
        
        # 更新底部状态
        layout["footer"].update(self._create_footer_panel())
    
    def _create_header_panel(self) -> Panel:
        """创建头部面板"""
        elapsed = time.time() - self.start_time
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        header_table = Table(show_header=False, box=None, padding=(0, 2))
        header_table.add_column("Key", style="cyan", width=15)
        header_table.add_column("Value", style="white")
        
        header_table.add_row("🎯 总试验数", str(self.total_trials))
        header_table.add_row("✅ 已完成", str(self.completed_trials))
        header_table.add_row("❌ 失败", str(self.failed_trials))
        header_table.add_row("🏃 运行中", str(len([t for t in self.trials.values() if t.status == "RUNNING"])))
        header_table.add_row("⏱️ 运行时间", f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")
        
        return Panel(
            header_table,
            title="[bold blue]🔥 Ray Tune 超参数优化[/bold blue]",
            border_style="blue"
        )
    
    def _create_trials_panel(self) -> Panel:
        """创建试验列表面板"""
        trials_table = Table(show_header=True, header_style="bold magenta")
        trials_table.add_column("试验ID", width=12)
        trials_table.add_column("状态", width=8)
        trials_table.add_column("WandB", width=8)
        trials_table.add_column("最佳指标", width=12)
        trials_table.add_column("配置", width=30)
        
        # 按状态排序显示试验
        sorted_trials = sorted(
            self.trials.values(),
            key=lambda x: (x.status != "RUNNING", x.last_update),
            reverse=True
        )
        
        # 只显示最近的10个试验
        for trial in sorted_trials[:10]:
            status_emoji = {
                "RUNNING": "🏃",
                "TERMINATED": "✅",
                "ERROR": "❌"
            }.get(trial.status, "❓")
            
            wandb_emoji = {
                "online": "🌐",
                "offline": "💾",
                "disabled": "🚫"
            }.get(trial.wandb_mode, "❓")
            
            # 格式化最佳指标
            best_metric = ""
            if trial.metrics:
                key_metric = max(trial.metrics.keys(), key=lambda k: trial.metrics[k])
                best_metric = f"{key_metric}: {trial.metrics[key_metric]:.4f}"
            
            # 格式化配置
            config_str = ", ".join([f"{k}={v}" for k, v in list(trial.config.items())[:3]])
            if len(trial.config) > 3:
                config_str += "..."
            
            trials_table.add_row(
                trial.trial_id[:10],
                f"{status_emoji} {trial.status}",
                f"{wandb_emoji} {trial.wandb_mode}",
                best_metric,
                config_str
            )
        
        return Panel(
            trials_table,
            title="[bold green]📊 试验状态[/bold green]",
            border_style="green"
        )
    
    def _create_best_panel(self) -> Panel:
        """创建最佳结果面板"""
        if not self.best_trial:
            return Panel(
                Align.center("暂无最佳结果", vertical="middle"),
                title="[bold yellow]🏆 最佳结果[/bold yellow]",
                border_style="yellow"
            )
        
        best_table = Table(show_header=False, box=None)
        best_table.add_column("Key", style="cyan", width=12)
        best_table.add_column("Value", style="white")
        
        best_table.add_row("🆔 试验ID", self.best_trial.trial_id[:10])
        best_table.add_row("📈 最佳指标", f"{self.best_metric_value:.4f}")
        
        # 显示最佳配置
        for key, value in list(self.best_trial.config.items())[:5]:
            best_table.add_row(f"⚙️ {key}", str(value))
        
        return Panel(
            best_table,
            title="[bold yellow]🏆 最佳结果[/bold yellow]",
            border_style="yellow"
        )
    
    def _create_footer_panel(self) -> Panel:
        """创建底部状态面板"""
        # WandB状态统计
        wandb_stats = {"online": 0, "offline": 0, "disabled": 0}
        for trial in self.trials.values():
            if trial.wandb_mode in wandb_stats:
                wandb_stats[trial.wandb_mode] += 1
        
        footer_text = Text()
        footer_text.append("📈 WandB状态: ", style="bold")
        footer_text.append(f"🌐 在线:{wandb_stats['online']} ", style="green")
        footer_text.append(f"💾 离线:{wandb_stats['offline']} ", style="yellow")
        footer_text.append(f"🚫 禁用:{wandb_stats['disabled']}", style="red")
        
        return Panel(
            Align.center(footer_text),
            border_style="dim"
        )
    
    def update_trial_status(self, trial_id: str, status: str, config: Dict[str, Any], 
                          metrics: Dict[str, float], wandb_info: Dict[str, str]):
        """更新试验状态"""
        if self.is_ray_tune_worker:
            # Worker进程通过队列发送更新
            update_data = {
                'type': 'trial_update',
                'trial_id': trial_id,
                'status': status,
                'config': config,
                'metrics': metrics,
                'wandb_info': wandb_info,
                'timestamp': time.time()
            }
            self.output_queue.put(update_data)
        else:
            # Master进程直接更新
            self._process_trial_update(trial_id, status, config, metrics, wandb_info)
    
    def _process_trial_update(self, trial_id: str, status: str, config: Dict[str, Any],
                            metrics: Dict[str, float], wandb_info: Dict[str, str]):
        """处理试验更新"""
        # 更新或创建试验状态
        if trial_id not in self.trials:
            self.trials[trial_id] = TrialStatus(
                trial_id=trial_id,
                status=status,
                config=config,
                metrics=metrics,
                wandb_mode=wandb_info.get('mode', 'unknown'),
                wandb_status=wandb_info.get('status', 'unknown'),
                last_update=time.time()
            )
            self.total_trials += 1
        else:
            trial = self.trials[trial_id]
            trial.status = status
            trial.metrics.update(metrics)
            trial.wandb_mode = wandb_info.get('mode', trial.wandb_mode)
            trial.wandb_status = wandb_info.get('status', trial.wandb_status)
            trial.last_update = time.time()
        
        # 更新统计
        if status == "TERMINATED":
            self.completed_trials += 1
        elif status == "ERROR":
            self.failed_trials += 1
        
        # 更新最佳结果
        if metrics:
            # 假设我们关注的是val/iou指标
            key_metric = metrics.get('val/iou', 0)
            if key_metric > self.best_metric_value:
                self.best_metric_value = key_metric
                self.best_trial = self.trials[trial_id]
    
    def _process_update(self, update_data: Dict[str, Any]):
        """处理更新数据"""
        if update_data['type'] == 'trial_update':
            self._process_trial_update(
                update_data['trial_id'],
                update_data['status'],
                update_data['config'],
                update_data['metrics'],
                update_data['wandb_info']
            )
    
    def _print_startup_banner(self):
        """打印启动横幅"""
        if self.display_mode == "quiet":
            return
        
        banner = Panel(
            "[bold blue]🚀 Ray Tune 超参数优化启动[/bold blue]\n"
            "使用 Ctrl+C 可以优雅停止优化过程",
            border_style="blue"
        )
        self.console.print(banner)
    
    def _print_final_summary(self):
        """打印最终总结"""
        if self.display_mode == "quiet":
            return
        
        elapsed = time.time() - self.start_time
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column("Key", style="cyan", width=20)
        summary_table.add_column("Value", style="white")
        
        summary_table.add_row("⏱️ 总运行时间", f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}")
        summary_table.add_row("🎯 总试验数", str(self.total_trials))
        summary_table.add_row("✅ 成功完成", str(self.completed_trials))
        summary_table.add_row("❌ 失败", str(self.failed_trials))
        
        if self.best_trial:
            summary_table.add_row("🏆 最佳试验", self.best_trial.trial_id)
            summary_table.add_row("📈 最佳指标", f"{self.best_metric_value:.4f}")
        
        summary_panel = Panel(
            summary_table,
            title="[bold green]🎉 Ray Tune 优化完成[/bold green]",
            border_style="green"
        )
        
        self.console.print()
        self.console.print(summary_panel)
        self.console.print()


class RayTuneCallback(Callback):
    """Ray Tune回调，用于与控制台管理器通信"""
    
    def __init__(self, console_manager: RayTuneConsoleManager):
        self.console_manager = console_manager
    
    def on_trial_start(self, iteration, trials, trial, **info):
        """试验开始时的回调"""
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="RUNNING",
            config=trial.config,
            metrics={},
            wandb_info=wandb_info
        )
    
    def on_trial_result(self, iteration, trials, trial, result, **info):
        """试验结果更新时的回调"""
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="RUNNING",
            config=trial.config,
            metrics=result,
            wandb_info=wandb_info
        )
    
    def on_trial_complete(self, iteration, trials, trial, **info):
        """试验完成时的回调"""
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="TERMINATED",
            config=trial.config,
            metrics=trial.last_result or {},
            wandb_info=wandb_info
        )
    
    def on_trial_error(self, iteration, trials, trial, **info):
        """试验错误时的回调"""
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="ERROR",
            config=trial.config,
            metrics={},
            wandb_info=wandb_info
        )
    
    def _extract_wandb_info(self, trial) -> Dict[str, str]:
        """从试验中提取WandB信息"""
        # 这里需要根据实际的WandB集成方式来提取信息
        return {
            'mode': 'auto',  # 可以从trial.config中获取
            'status': 'unknown'
        }


# 全局Ray Tune控制台管理器实例
_raytune_console_manager: Optional[RayTuneConsoleManager] = None


def get_raytune_console_manager() -> RayTuneConsoleManager:
    """获取全局Ray Tune控制台管理器实例"""
    global _raytune_console_manager
    if _raytune_console_manager is None:
        _raytune_console_manager = RayTuneConsoleManager()
    return _raytune_console_manager


def setup_raytune_console_manager(display_mode: str = "dashboard", refresh_rate: float = 2.0) -> RayTuneConsoleManager:
    """设置全局Ray Tune控制台管理器"""
    global _raytune_console_manager
    _raytune_console_manager = RayTuneConsoleManager(display_mode, refresh_rate)
    return _raytune_console_manager
