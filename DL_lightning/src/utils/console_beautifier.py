#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
轻量级控制台美化器

设计原则:
- 最小侵入性，不破坏现有功能
- 只美化关键信息，不过度设计
- 兼容所有训练模式
- 可以轻松禁用
"""

import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional
from omegaconf import DictConfig

# 尝试导入Rich，如果失败则使用基础输出
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.text import Text
    from rich.columns import Columns
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class ConsoleBeautifier:
    """轻量级控制台美化器"""
    
    def __init__(self, enabled: bool = True, use_color: bool = True):
        """
        初始化美化器
        
        Args:
            enabled: 是否启用美化
            use_color: 是否使用颜色
        """
        self.enabled = enabled and RICH_AVAILABLE
        self.use_color = use_color and not os.getenv('NO_COLOR')
        
        if self.enabled:
            self.console = Console(
                force_terminal=True,
                color_system="auto" if self.use_color else None,
                width=100
            )
        else:
            self.console = None
        
        # 检测运行环境
        self.is_ray_tune_worker = self._is_ray_tune_worker()
        self.is_jupyter = self._is_jupyter()
        self.is_ci = self._is_ci()

        # 如果在Ray Tune worker中，自动禁用美化
        if self.is_ray_tune_worker:
            self.enabled = False
    
    def _is_ray_tune_worker(self) -> bool:
        """检测是否在Ray Tune worker进程中"""
        return (
            os.getenv('RAY_TRIAL_ID') is not None or
            os.getenv('TUNE_TRIAL_ID') is not None
        )
    
    def _is_jupyter(self) -> bool:
        """检测是否在Jupyter环境中"""
        return 'ipykernel' in sys.modules

    def _is_ci(self) -> bool:
        """检测是否在CI/CD环境中"""
        ci_indicators = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI']
        return any(os.getenv(indicator) for indicator in ci_indicators)
    
    def print_startup_banner(self, config: DictConfig):
        """打印启动横幅"""
        if not self.enabled:
            self._print_simple_startup(config)
            return
        
        # 创建项目信息
        info_lines = [
            f"🚀 项目: DL_lightning",
            f"📊 实验: {config.get('experiment_name', 'Unknown')}",
            f"🤖 模型: {config.model.name}",
            f"📁 数据集: {Path(config.data.dataset_config.data_dir).name}",
            f"🎯 类别数: {config.data.dataset_config.num_classes}",
        ]
        
        # WandB状态
        if 'wandb' in config:
            wandb_mode = config.wandb.get('mode', 'unknown')
            wandb_emoji = {'online': '🌐', 'offline': '💾', 'disabled': '🚫', 'auto': '🔄'}.get(wandb_mode, '❓')
            info_lines.append(f"📈 WandB: {wandb_emoji} {wandb_mode}")
        
        # 设备信息
        try:
            import torch
            if torch.cuda.is_available():
                device_name = torch.cuda.get_device_name(0)
                info_lines.append(f"💻 设备: 🎮 {device_name}")
            else:
                info_lines.append(f"💻 设备: 💻 CPU")
        except:
            pass
        
        # 创建面板
        info_text = "\n".join(info_lines)
        banner = Panel(
            info_text,
            title="[bold blue]🔥 训练启动[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(banner)
        self.console.print()
    
    def _print_simple_startup(self, config: DictConfig):
        """简单的启动信息（无Rich时使用）"""
        print("\n" + "="*60)
        print("🔥 DL_lightning 训练启动")
        print("="*60)
        print(f"📊 实验: {config.get('experiment_name', 'Unknown')}")
        print(f"🤖 模型: {config.model.name}")
        print(f"📁 数据集: {Path(config.data.dataset_config.data_dir).name}")
        print(f"🎯 类别数: {config.data.dataset_config.num_classes}")
        
        if 'wandb' in config:
            wandb_mode = config.wandb.get('mode', 'unknown')
            print(f"📈 WandB: {wandb_mode}")
        
        print("="*60 + "\n")
    
    def print_wandb_status(self, wandb_status: Dict[str, Any]):
        """打印WandB状态"""
        if not self.enabled:
            self._print_simple_wandb_status(wandb_status)
            return
        
        # 创建状态表格
        status_table = Table(show_header=False, box=None, padding=(0, 1))
        status_table.add_column("Key", style="cyan", width=12)
        status_table.add_column("Value", style="white")
        
        # 模式状态
        mode = wandb_status.get('mode', 'unknown')
        mode_emoji = {'online': '🌐', 'offline': '💾', 'disabled': '🚫', 'auto': '🔄'}.get(mode, '❓')
        status_table.add_row("模式", f"{mode_emoji} {mode}")
        
        # 网络状态
        if wandb_status.get('internet_available') is not None:
            network_status = "✅ 可用" if wandb_status['internet_available'] else "❌ 不可用"
            status_table.add_row("网络", network_status)
        
        # 本地目录
        local_dir = Path(wandb_status.get('local_dir', '')).name
        if local_dir:
            status_table.add_row("本地目录", local_dir)
        
        # 离线运行数量
        offline_count = wandb_status.get('offline_runs_count', 0)
        if offline_count > 0:
            status_table.add_row("离线运行", f"📦 {offline_count}")
        
        wandb_panel = Panel(
            status_table,
            title="[bold green]📈 WandB 状态[/bold green]",
            border_style="green",
            padding=(0, 1)
        )
        
        self.console.print(wandb_panel)
        self.console.print()
    
    def _print_simple_wandb_status(self, wandb_status: Dict[str, Any]):
        """简单的WandB状态显示"""
        print("\n" + "-"*40)
        print("📈 WandB 状态")
        print("-"*40)
        
        mode = wandb_status.get('mode', 'unknown')
        print(f"模式: {mode}")
        
        if wandb_status.get('internet_available') is not None:
            network_status = "可用" if wandb_status['internet_available'] else "不可用"
            print(f"网络: {network_status}")
        
        local_dir = Path(wandb_status.get('local_dir', '')).name
        if local_dir:
            print(f"本地目录: {local_dir}")
        
        print("-"*40 + "\n")
    
    def print_training_summary(self, final_metrics: Dict[str, float], duration: float):
        """打印训练总结"""
        if not self.enabled:
            self._print_simple_summary(final_metrics, duration)
            return
        
        # 格式化时间
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        # 创建总结表格
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column("指标", style="cyan", width=15)
        summary_table.add_column("值", style="bold white")
        
        summary_table.add_row("⏱️ 训练时间", time_str)
        
        # 添加最终指标
        for key, value in final_metrics.items():
            if isinstance(value, (int, float)):
                summary_table.add_row(f"📊 {key}", f"{value:.4f}")
        
        summary_panel = Panel(
            summary_table,
            title="[bold green]🎉 训练完成[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(summary_panel)
        self.console.print()
    
    def _print_simple_summary(self, final_metrics: Dict[str, float], duration: float):
        """简单的训练总结"""
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        print("\n" + "="*50)
        print("🎉 训练完成")
        print("="*50)
        print(f"⏱️ 训练时间: {time_str}")
        
        for key, value in final_metrics.items():
            if isinstance(value, (int, float)):
                print(f"📊 {key}: {value:.4f}")
        
        print("="*50 + "\n")
    
    def print_error(self, error: Exception, context: str = ""):
        """打印错误信息"""
        if not self.enabled:
            self._print_simple_error(error, context)
            return
        
        error_text = Text()
        error_text.append("❌ 错误: ", style="bold red")
        error_text.append(str(error), style="red")
        
        if context:
            error_text.append(f"\n📍 上下文: {context}", style="yellow")
        
        error_panel = Panel(
            error_text,
            title="[bold red]⚠️ 错误[/bold red]",
            border_style="red",
            padding=(1, 2)
        )
        
        self.console.print(error_panel)
    
    def _print_simple_error(self, error: Exception, context: str = ""):
        """简单的错误显示"""
        print(f"\n❌ 错误: {error}")
        if context:
            print(f"📍 上下文: {context}")
        print()
    
    def print_info(self, message: str, emoji: str = "ℹ️"):
        """打印信息"""
        if self.enabled:
            info_text = Text()
            info_text.append(f"{emoji} ", style="bold blue")
            info_text.append(message, style="blue")
            self.console.print(info_text)
        else:
            print(f"{emoji} {message}")

    def print_training_summary(self, final_metrics: Dict[str, float], duration: float):
        """打印训练总结"""
        if not self.enabled:
            self._print_simple_training_summary(final_metrics, duration)
            return

        # 格式化时间
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

        # 创建总结表格
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column("指标", style="cyan", width=20)
        summary_table.add_column("值", style="bold white")

        summary_table.add_row("⏱️ 训练时间", time_str)

        # 添加最终指标
        for key, value in final_metrics.items():
            if isinstance(value, (int, float)):
                # 格式化指标名称
                display_key = key.replace('val/', '验证 ').replace('train/', '训练 ')
                summary_table.add_row(f"📊 {display_key}", f"{value:.4f}")

        summary_panel = Panel(
            summary_table,
            title="[bold green]🎉 训练完成[/bold green]",
            border_style="green",
            padding=(1, 2)
        )

        self.console.print()
        self.console.print(summary_panel)
        self.console.print()

    def _print_simple_training_summary(self, final_metrics: Dict[str, float], duration: float):
        """简单的训练总结"""
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

        print("\n" + "="*50)
        print("🎉 训练完成")
        print("="*50)
        print(f"⏱️ 训练时间: {time_str}")

        for key, value in final_metrics.items():
            if isinstance(value, (int, float)):
                display_key = key.replace('val/', '验证 ').replace('train/', '训练 ')
                print(f"📊 {display_key}: {value:.4f}")

        print("="*50 + "\n")
    
    def print_success(self, message: str):
        """打印成功信息"""
        if self.enabled:
            success_text = Text()
            success_text.append("✅ ", style="bold green")
            success_text.append(message, style="green")
            self.console.print(success_text)
        else:
            print(f"✅ {message}")
    
    def print_warning(self, message: str):
        """打印警告信息"""
        if self.enabled:
            warning_text = Text()
            warning_text.append("⚠️ ", style="bold yellow")
            warning_text.append(message, style="yellow")
            self.console.print(warning_text)
        else:
            print(f"⚠️ {message}")
    
    def print_ray_tune_info(self, message: str):
        """Ray Tune专用信息显示"""
        if self.is_ray_tune_worker:
            # Worker进程使用简化输出
            print(f"[Ray Tune] {message}")
        else:
            # Master进程使用美化输出
            self.print_info(message, "🎯")


# 全局美化器实例
_beautifier: Optional[ConsoleBeautifier] = None


def get_console_beautifier() -> ConsoleBeautifier:
    """获取全局美化器实例"""
    global _beautifier
    if _beautifier is None:
        # 从环境变量读取配置
        enabled = os.getenv('CONSOLE_BEAUTIFY', 'true').lower() in ('true', '1', 'yes')
        use_color = os.getenv('CONSOLE_COLOR', 'true').lower() in ('true', '1', 'yes')
        _beautifier = ConsoleBeautifier(enabled=enabled, use_color=use_color)
    return _beautifier


def setup_console_beautifier(enabled: bool = True, use_color: bool = True) -> ConsoleBeautifier:
    """设置全局美化器"""
    global _beautifier
    _beautifier = ConsoleBeautifier(enabled=enabled, use_color=use_color)
    return _beautifier
