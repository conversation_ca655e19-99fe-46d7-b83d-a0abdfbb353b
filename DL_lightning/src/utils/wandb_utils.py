#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WandB工具模块 - 支持本地化和云端模式灵活切换

功能特性:
- 支持online/offline/disabled三种模式
- 网络容错和自动降级
- 本地数据备份和后续同步
- 环境变量和配置文件双重支持
- 与Lightning + Hydra无缝集成
"""

import os
import logging
import time
import socket
from pathlib import Path
from typing import Optional, Dict, Any, Union
from omegaconf import DictConfig
import wandb
from lightning.pytorch.loggers import WandbLogger


logger = logging.getLogger(__name__)


class WandbModeManager:
    """WandB模式管理器 - 处理不同运行模式的切换和配置"""
    
    SUPPORTED_MODES = ["online", "offline", "disabled"]
    DEFAULT_MODE = "online"
    
    def __init__(self, config: DictConfig):
        """
        初始化WandB模式管理器
        
        Args:
            config: Hydra配置对象
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 确定运行模式
        self.mode = self._determine_mode()
        self.logger.info(f"WandB模式已设置为: {self.mode}")
        
        # 设置本地存储路径
        self.local_dir = self._setup_local_directory()
        
        # 配置WandB环境
        self._configure_wandb_environment()
    
    def _determine_mode(self) -> str:
        """
        确定WandB运行模式
        
        优先级:
        1. 环境变量 WANDB_MODE
        2. 配置文件中的 wandb.mode
        3. 网络连接检测 (如果配置为auto)
        4. 默认模式 (online)
        """
        # 1. 检查环境变量
        env_mode = os.getenv("WANDB_MODE", "").lower()
        if env_mode in self.SUPPORTED_MODES:
            self.logger.info(f"使用环境变量指定的WandB模式: {env_mode}")
            return env_mode
        
        # 2. 检查配置文件
        config_mode = getattr(self.config.wandb, 'mode', 'auto').lower()
        if config_mode in self.SUPPORTED_MODES:
            self.logger.info(f"使用配置文件指定的WandB模式: {config_mode}")
            return config_mode
        
        # 3. 自动检测模式
        if config_mode == "auto":
            detected_mode = self._detect_optimal_mode()
            self.logger.info(f"自动检测到最佳WandB模式: {detected_mode}")
            return detected_mode
        
        # 4. 默认模式
        self.logger.warning(f"未识别的WandB模式配置: {config_mode}, 使用默认模式: {self.DEFAULT_MODE}")
        return self.DEFAULT_MODE
    
    def _detect_optimal_mode(self) -> str:
        """
        自动检测最佳运行模式
        
        检测逻辑:
        - 尝试连接WandB服务器
        - 检查网络连接状态
        - 根据连接结果决定模式
        """
        try:
            # 检查网络连接
            if self._check_internet_connection():
                # 尝试连接WandB API
                if self._check_wandb_connection():
                    return "online"
                else:
                    self.logger.warning("WandB服务不可用，切换到离线模式")
                    return "offline"
            else:
                self.logger.warning("网络连接不可用，切换到离线模式")
                return "offline"
        except Exception as e:
            self.logger.error(f"模式检测失败: {e}, 使用离线模式")
            return "offline"
    
    def _check_internet_connection(self, timeout: int = 5) -> bool:
        """检查网络连接"""
        try:
            socket.create_connection(("8.8.8.8", 53), timeout)
            return True
        except OSError:
            return False
    
    def _check_wandb_connection(self, timeout: int = 10) -> bool:
        """检查WandB服务连接"""
        try:
            # 尝试简单的API调用
            import requests
            response = requests.get("https://api.wandb.ai/", timeout=timeout)
            return response.status_code == 200
        except Exception:
            return False
    
    def _setup_local_directory(self) -> Path:
        """设置本地存储目录"""
        # 从配置或环境变量获取本地目录
        local_dir = getattr(self.config.wandb, 'local_dir', None)
        if not local_dir:
            # 使用Hydra的输出目录
            from hydra.core.hydra_config import HydraConfig
            try:
                hydra_cfg = HydraConfig.get()
                local_dir = Path(hydra_cfg.runtime.output_dir) / "wandb_local"
            except Exception:
                # 回退到当前目录
                local_dir = Path.cwd() / "outputs" / "wandb_local"
        else:
            local_dir = Path(local_dir)
        
        # 创建目录
        local_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"WandB本地存储目录: {local_dir}")
        
        return local_dir
    
    def _configure_wandb_environment(self):
        """配置WandB环境变量"""
        # 设置基本环境变量
        os.environ["WANDB_MODE"] = self.mode
        os.environ["WANDB_DIR"] = str(self.local_dir)
        
        # 根据模式设置特定环境变量
        if self.mode == "offline":
            os.environ["WANDB_OFFLINE"] = "true"
            self.logger.info("WandB配置为离线模式，数据将保存到本地")
        elif self.mode == "disabled":
            os.environ["WANDB_DISABLED"] = "true"
            self.logger.info("WandB已禁用，不会记录任何数据")
        elif self.mode == "online":
            # 清除可能的离线设置
            os.environ.pop("WANDB_OFFLINE", None)
            os.environ.pop("WANDB_DISABLED", None)
            self.logger.info("WandB配置为在线模式，数据将同步到云端")
        
        # 设置其他有用的环境变量
        if hasattr(self.config.wandb, 'api_key') and self.config.wandb.api_key:
            os.environ["WANDB_API_KEY"] = self.config.wandb.api_key
        
        # 设置静默模式以减少输出
        if getattr(self.config.wandb, 'silent', True):
            os.environ["WANDB_SILENT"] = "true"
    
    def create_logger(self) -> Optional[WandbLogger]:
        """
        创建WandB Logger实例
        
        Returns:
            WandbLogger实例，如果模式为disabled则返回None
        """
        if self.mode == "disabled":
            self.logger.info("WandB已禁用，跳过logger创建")
            return None
        
        try:
            # 准备logger参数
            logger_kwargs = {
                "project": self.config.wandb.project,
                "name": self.config.wandb.name,
                "save_dir": str(self.local_dir),
                "offline": (self.mode == "offline"),
                "tags": getattr(self.config.wandb, 'tags', []),
                "notes": getattr(self.config.wandb, 'notes', ""),
                "log_model": getattr(self.config.wandb, 'log_model', False),
            }
            
            # 创建logger
            wandb_logger = WandbLogger(**logger_kwargs)
            
            # 记录模式信息
            if wandb_logger.experiment:
                wandb_logger.experiment.config.update({
                    "wandb_mode": self.mode,
                    "local_dir": str(self.local_dir),
                    "auto_sync": self.mode == "online"
                })
            
            self.logger.info(f"WandB Logger创建成功 (模式: {self.mode})")
            return wandb_logger
            
        except Exception as e:
            self.logger.error(f"WandB Logger创建失败: {e}")
            if self.mode == "online":
                self.logger.info("尝试降级到离线模式...")
                self.mode = "offline"
                os.environ["WANDB_MODE"] = "offline"
                os.environ["WANDB_OFFLINE"] = "true"
                return self.create_logger()  # 递归重试
            return None
    
    def sync_offline_data(self, run_path: Optional[str] = None) -> bool:
        """
        同步离线数据到云端
        
        Args:
            run_path: 特定运行的路径，如果为None则同步所有离线数据
            
        Returns:
            同步是否成功
        """
        if self.mode != "offline":
            self.logger.warning("只有离线模式的数据才需要同步")
            return False
        
        try:
            if run_path:
                # 同步特定运行
                result = wandb.sync(run_path)
            else:
                # 同步所有离线数据
                offline_runs = list(self.local_dir.glob("**/run-*.wandb"))
                if not offline_runs:
                    self.logger.info("没有找到需要同步的离线数据")
                    return True
                
                success_count = 0
                for run_file in offline_runs:
                    try:
                        wandb.sync(str(run_file.parent))
                        success_count += 1
                        self.logger.info(f"成功同步: {run_file.parent}")
                    except Exception as e:
                        self.logger.error(f"同步失败 {run_file.parent}: {e}")
                
                self.logger.info(f"同步完成: {success_count}/{len(offline_runs)} 个运行成功")
                return success_count == len(offline_runs)
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            return False
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取当前状态信息"""
        return {
            "mode": self.mode,
            "local_dir": str(self.local_dir),
            "internet_available": self._check_internet_connection(),
            "wandb_service_available": self._check_wandb_connection() if self.mode == "online" else None,
            "offline_runs_count": len(list(self.local_dir.glob("**/run-*.wandb"))) if self.local_dir.exists() else 0
        }


def create_wandb_logger(config: DictConfig) -> Optional[WandbLogger]:
    """
    便捷函数：创建WandB Logger
    
    Args:
        config: Hydra配置对象
        
    Returns:
        WandbLogger实例或None
    """
    manager = WandbModeManager(config)
    return manager.create_logger()


def sync_wandb_data(config: DictConfig, run_path: Optional[str] = None) -> bool:
    """
    便捷函数：同步WandB离线数据
    
    Args:
        config: Hydra配置对象
        run_path: 特定运行路径
        
    Returns:
        同步是否成功
    """
    manager = WandbModeManager(config)
    return manager.sync_offline_data(run_path)


def get_wandb_status(config: DictConfig) -> Dict[str, Any]:
    """
    便捷函数：获取WandB状态信息
    
    Args:
        config: Hydra配置对象
        
    Returns:
        状态信息字典
    """
    manager = WandbModeManager(config)
    return manager.get_status_info()
