#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一控制台输出管理器

功能特性:
- 统一的Rich风格输出
- 多种训练模式的协调显示
- 系统资源监控
- 美观的进度条和状态显示
- 与Lightning + WandB + Ray Tune协调
"""

import os
import sys
import time
import psutil
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from contextlib import contextmanager

import torch
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.table import Table
from rich.columns import Columns
from rich.text import Text
from rich.progress import (
    Progress, SpinnerColumn, TextColumn, BarColumn, 
    MofNCompleteColumn, TimeRemainingColumn, TimeElapsedColumn
)
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
from omegaconf import DictConfig

# GPU监控
try:
    import pynvml
    pynvml.nvmlInit()
    PYNVML_AVAILABLE = True
except (ImportError, Exception):
    pynvml = None
    PYNVML_AVAILABLE = False


class ConsoleManager:
    """统一控制台输出管理器"""
    
    def __init__(self, config: Optional[DictConfig] = None, verbose: bool = True):
        """
        初始化控制台管理器
        
        Args:
            config: 配置对象
            verbose: 是否启用详细输出
        """
        self.config = config
        self.verbose = verbose
        self.console = Console(
            force_terminal=True,
            color_system="auto",
            width=120,
            legacy_windows=False
        )
        
        # 输出模式配置
        self.quiet_mode = getattr(config, 'quiet', False) if config else False
        self.no_color = os.getenv('NO_COLOR', '').lower() in ('1', 'true', 'yes')
        
        # 状态跟踪
        self.training_started = False
        self.current_epoch = 0
        self.total_epochs = 0
        self.best_metrics = {}
        
        # 进度条管理
        self.progress = None
        self.live_display = None
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置统一的日志格式"""
        # 清除现有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置日志级别
        log_level = logging.DEBUG if self.verbose else logging.INFO
        if self.quiet_mode:
            log_level = logging.WARNING
        
        # 创建Rich处理器
        rich_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_level=True,
            show_path=False,
            rich_tracebacks=True,
            tracebacks_suppress=[torch],
            markup=True,
            log_time_format="[%H:%M:%S]"
        )
        rich_handler.setLevel(log_level)
        
        # 设置格式
        formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]"
        )
        rich_handler.setFormatter(formatter)
        
        # 配置根日志器
        root_logger.setLevel(log_level)
        root_logger.addHandler(rich_handler)
        
        # 设置特定模块的日志级别
        logging.getLogger("lightning").setLevel(logging.WARNING)
        logging.getLogger("torch").setLevel(logging.WARNING)
        logging.getLogger("torchmetrics").setLevel(logging.WARNING)
    
    def print_startup_banner(self, config: DictConfig):
        """打印启动横幅"""
        if self.quiet_mode:
            return
        
        # 创建项目信息表格
        info_table = Table(show_header=False, box=None, padding=(0, 2))
        info_table.add_column("Key", style="cyan", width=20)
        info_table.add_column("Value", style="white")
        
        info_table.add_row("🚀 项目", "DL_lightning")
        info_table.add_row("📊 实验", config.get('experiment_name', 'Unknown'))
        info_table.add_row("🏷️ 运行名称", config.get('run_name', 'Unknown'))
        info_table.add_row("🤖 模型", config.model.name)
        info_table.add_row("📁 数据集", Path(config.data.data_dir).name)
        info_table.add_row("🎯 类别数", str(config.data.num_classes))
        
        # WandB状态
        if 'wandb' in config:
            wandb_mode = config.wandb.get('mode', 'unknown')
            wandb_status = {
                'online': '🌐 在线模式',
                'offline': '💾 离线模式', 
                'disabled': '🚫 已禁用',
                'auto': '🔄 自动模式'
            }.get(wandb_mode, f'❓ {wandb_mode}')
            info_table.add_row("📈 WandB", wandb_status)
        
        # 系统信息
        device_info = self._get_device_info()
        info_table.add_row("💻 设备", device_info)
        
        # 创建面板
        banner_panel = Panel(
            info_table,
            title="[bold blue]🔥 DL_lightning 训练启动[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(banner_panel)
        self.console.print()
    
    def print_wandb_status(self, wandb_status: Dict[str, Any]):
        """打印WandB状态信息"""
        if self.quiet_mode:
            return
        
        status_table = Table(show_header=False, box=None, padding=(0, 1))
        status_table.add_column("Key", style="cyan", width=15)
        status_table.add_column("Value", style="white")
        
        # 模式状态
        mode = wandb_status.get('mode', 'unknown')
        mode_emoji = {
            'online': '🌐',
            'offline': '💾',
            'disabled': '🚫',
            'auto': '🔄'
        }.get(mode, '❓')
        status_table.add_row("模式", f"{mode_emoji} {mode}")
        
        # 网络状态
        network_status = "✅ 可用" if wandb_status.get('internet_available') else "❌ 不可用"
        status_table.add_row("网络连接", network_status)
        
        # WandB服务状态
        if wandb_status.get('wandb_service_available') is not None:
            service_status = "✅ 可用" if wandb_status['wandb_service_available'] else "❌ 不可用"
            status_table.add_row("WandB服务", service_status)
        
        # 本地目录
        local_dir = Path(wandb_status.get('local_dir', '')).name
        status_table.add_row("本地目录", local_dir)
        
        # 离线运行数量
        offline_count = wandb_status.get('offline_runs_count', 0)
        if offline_count > 0:
            status_table.add_row("离线运行", f"📦 {offline_count} 个")
        
        wandb_panel = Panel(
            status_table,
            title="[bold green]📈 WandB 状态[/bold green]",
            border_style="green",
            padding=(0, 1)
        )
        
        self.console.print(wandb_panel)
        self.console.print()
    
    def print_config_summary(self, config: DictConfig):
        """打印配置摘要"""
        if self.quiet_mode:
            return
        
        # 创建配置摘要表格
        config_table = Table(show_header=True, box=None)
        config_table.add_column("组件", style="cyan", width=15)
        config_table.add_column("配置", style="white")
        
        # 模型配置
        model_info = f"{config.model.name}"
        if hasattr(config.model, 'model_params') and 'backbone' in config.model.model_params:
            model_info += f" ({config.model.model_params.backbone})"
        config_table.add_row("🤖 模型", model_info)
        
        # 数据配置
        data_info = f"批次大小: {config.data.batch_size}, 图像尺寸: {config.data.image_size}"
        config_table.add_row("📊 数据", data_info)
        
        # 训练配置
        train_info = f"最大轮数: {config.trainer.max_epochs}, 精度: {config.trainer.precision}"
        config_table.add_row("🏋️ 训练", train_info)
        
        # 优化器配置
        opt_name = config.optimizer._target_.split('.')[-1]
        opt_info = f"{opt_name} (lr: {config.optimizer.lr})"
        config_table.add_row("⚡ 优化器", opt_info)
        
        # 调度器配置
        if config.scheduler:
            sched_name = config.scheduler._target_.split('.')[-1]
            config_table.add_row("📈 调度器", sched_name)
        
        config_panel = Panel(
            config_table,
            title="[bold yellow]⚙️ 配置摘要[/bold yellow]",
            border_style="yellow",
            padding=(0, 1)
        )
        
        self.console.print(config_panel)
        self.console.print()
    
    def _get_device_info(self) -> str:
        """获取设备信息"""
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            device_count = torch.cuda.device_count()
            return f"🎮 {device_name} (x{device_count})"
        else:
            return "💻 CPU"
    
    def _get_system_stats(self) -> Dict[str, str]:
        """获取系统资源状态"""
        stats = {}
        
        # CPU和内存
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        stats['cpu'] = f"💻 CPU {cpu_percent:.1f}%"
        stats['memory'] = f"🧠 RAM {memory.used/1024**3:.1f}/{memory.total/1024**3:.1f}GB"
        
        # GPU信息
        if PYNVML_AVAILABLE and torch.cuda.is_available():
            try:
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                stats['gpu'] = f"🎮 GPU {util.gpu}%"
                stats['vram'] = f"📺 VRAM {mem_info.used/1024**3:.1f}/{mem_info.total/1024**3:.1f}GB"
            except Exception:
                stats['gpu'] = "🎮 GPU N/A"
                stats['vram'] = "📺 VRAM N/A"
        
        return stats
    
    def create_training_progress(self, total_epochs: int) -> Progress:
        """创建训练进度条"""
        if self.quiet_mode:
            return None
        
        self.total_epochs = total_epochs
        
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            MofNCompleteColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            "•",
            TimeElapsedColumn(),
            "<",
            TimeRemainingColumn(),
            console=self.console,
            transient=False
        )
        
        return progress
    
    def print_training_summary(self, logs: Dict[str, Any], duration: float):
        """打印训练总结"""
        if self.quiet_mode:
            return
        
        # 创建总结表格
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column("指标", style="cyan", width=20)
        summary_table.add_column("值", style="bold white")
        
        # 训练时间
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        summary_table.add_row("⏱️ 训练时间", time_str)
        
        # 最终指标
        if 'val/loss' in logs:
            summary_table.add_row("📉 最终验证损失", f"{logs['val/loss']:.4f}")
        if 'val/iou' in logs:
            summary_table.add_row("🎯 最终IoU", f"{logs['val/iou']:.4f}")
        if 'val/dice' in logs:
            summary_table.add_row("🎲 最终Dice", f"{logs['val/dice']:.4f}")
        
        # 最佳指标
        for metric, value in self.best_metrics.items():
            summary_table.add_row(f"🏆 最佳{metric}", f"{value:.4f}")
        
        # 系统资源
        final_stats = self._get_system_stats()
        for key, value in final_stats.items():
            summary_table.add_row(f"📊 {key.upper()}", value)
        
        summary_panel = Panel(
            summary_table,
            title="[bold green]🎉 训练完成总结[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(summary_panel)
        self.console.print()
    
    def print_error(self, error: Exception, context: str = ""):
        """打印错误信息"""
        error_text = Text()
        error_text.append("❌ 错误: ", style="bold red")
        error_text.append(str(error), style="red")
        
        if context:
            error_text.append(f"\n📍 上下文: {context}", style="yellow")
        
        error_panel = Panel(
            error_text,
            title="[bold red]⚠️ 错误信息[/bold red]",
            border_style="red",
            padding=(1, 2)
        )
        
        self.console.print(error_panel)
    
    def print_warning(self, message: str, context: str = ""):
        """打印警告信息"""
        warning_text = Text()
        warning_text.append("⚠️ 警告: ", style="bold yellow")
        warning_text.append(message, style="yellow")
        
        if context:
            warning_text.append(f"\n📍 上下文: {context}", style="dim")
        
        self.console.print(warning_text)
    
    def print_success(self, message: str):
        """打印成功信息"""
        success_text = Text()
        success_text.append("✅ ", style="bold green")
        success_text.append(message, style="green")
        
        self.console.print(success_text)
    
    def print_info(self, message: str, emoji: str = "ℹ️"):
        """打印信息"""
        if not self.quiet_mode:
            info_text = Text()
            info_text.append(f"{emoji} ", style="bold blue")
            info_text.append(message, style="blue")
            self.console.print(info_text)
    
    @contextmanager
    def status(self, message: str):
        """状态上下文管理器"""
        if self.quiet_mode:
            yield
        else:
            with self.console.status(f"[bold blue]{message}[/bold blue]") as status:
                yield status
    
    def update_best_metrics(self, metrics: Dict[str, float]):
        """更新最佳指标"""
        for key, value in metrics.items():
            if key not in self.best_metrics or value > self.best_metrics[key]:
                self.best_metrics[key] = value
    
    def cleanup(self):
        """清理资源"""
        if PYNVML_AVAILABLE and pynvml:
            try:
                pynvml.nvmlShutdown()
            except Exception:
                pass


# 全局控制台管理器实例
_console_manager: Optional[ConsoleManager] = None


def get_console_manager() -> ConsoleManager:
    """获取全局控制台管理器实例"""
    global _console_manager
    if _console_manager is None:
        _console_manager = ConsoleManager()
    return _console_manager


def setup_console_manager(config: Optional[DictConfig] = None, verbose: bool = True) -> ConsoleManager:
    """设置全局控制台管理器"""
    global _console_manager
    _console_manager = ConsoleManager(config, verbose)
    return _console_manager
