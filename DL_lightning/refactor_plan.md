# 数据模块重构计划

## 🎯 目标：遵循PyTorch Lightning标准

### 当前结构问题
1. 文件命名不一致：`suide_dataset.py` vs `loveda.py`
2. 类名不一致：`SuiDeDatasetV2` vs `LoveDADataset`
3. 目录结构过度复杂：不必要的分层
4. 与Lightning生态不一致

### 重构方案：Lightning标准结构

```
src/data/
├── components/                    # 可复用组件
│   ├── __init__.py
│   ├── transforms.py             # 重命名transforms_lightning.py
│   └── class_mapper.py           # 提取通用类别映射逻辑
├── suide_datamodule.py           # 合并datamodules/suide.py + datasets/suide_dataset.py
├── loveda_datamodule.py          # 合并datamodules/loveda.py + datasets/loveda.py
└── __init__.py
```

### 重构步骤

#### 步骤1: 创建components目录
- 移动transforms_lightning.py -> components/transforms.py
- 提取ClassMapper到components/class_mapper.py

#### 步骤2: 合并DataModule和Dataset
- 将SuiDeDataModule和SuiDeDatasetV2合并到suide_datamodule.py
- 将LoveDADataModule和LoveDADataset合并到loveda_datamodule.py

#### 步骤3: 统一命名
- 类名：SuiDeDataModule, LoveDADataModule
- 内部Dataset类：SuiDeDataset, LoveDADataset（去掉V2）

#### 步骤4: 更新导入
- 更新所有import语句
- 更新配置文件中的_target_路径

#### 步骤5: 删除旧目录
- 删除datamodules/目录
- 删除datasets/目录

### 预期收益
1. 符合Lightning标准，新人容易理解
2. 文件命名一致，减少困惑
3. 减少目录层级，简化结构
4. 更好的代码组织和复用性

### 风险评估
- 低风险：主要是文件移动和重命名
- 需要更新测试和配置文件
- 向后兼容性通过__init__.py中的别名保持
