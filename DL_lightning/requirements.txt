# === 核心依赖 Core Dependencies ===

# --- 深度学习框架 (Deep Learning Framework) ---
# PyTorch: 核心计算库
# Torchvision: 视觉处理库
torch>=2.3.1
torchvision>=0.18.1

# --- 训练引擎 (Training Engine) ---
# Lightning: (原Pytorch-Lightning) 高级训练框架，简化代码
lightning>=2.3.0
# TorchMetrics: PyTorch 指标库，从 Lightning 中解耦
torchmetrics>=1.4.0

# --- 实验跟踪与超参优化 (Experiment Tracking & HPO) ---
# Weights & Biases: 用于记录、可视化和比较实验结果
wandb>=0.17.0
# Ray Tune: 分布式超参数搜索引擎
ray[tune]>=2.10.0
# Ray Lightning: Ray与Lightning的集成插件
ray_lightning>=0.2.0
# Optuna: 备选的超参数优化框架
optuna>=3.6.0

# --- 配置管理 (Configuration Management) ---
# Hydra: 强大且灵活的应用配置工具
hydra-core>=1.3.2
# OmegaConf: Hydra使用的YAML/Dict配置解析库
omegaconf>=2.3.0
# Jsonargparse: Lightning依赖，用于命令行参数解析
jsonargparse[signatures]>=4.27.0

# === 数据处理与工具库 Data Processing & Utilities ===

# --- 数据增强与图像处理 (Data Augmentation & Image Processing) ---
albumentations>=1.4.0
opencv-python>=4.9.0
pillow>=10.3.0

# --- 常用工具 (Common Utilities) ---
rich>=13.7.0          # 终端富文本输出
tqdm>=4.66.0          # 进度条
matplotlib>=3.9.0     # 绘图
seaborn>=0.13.0       # 统计可视化
pandas>=2.2.0         # 数据分析
numpy>=1.26.0         # 科学计算

# === 开发与代码质量 (Development & Code Quality) ===
pytest>=8.2.0         # 测试框架
black>=24.4.0         # 代码格式化
isort>=5.13.0         # import排序
mypy>=1.10.0          # 静态类型检查
