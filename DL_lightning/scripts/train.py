import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
import lightning.pytorch as pl
from lightning.pytorch.loggers import <PERSON><PERSON>
from typing import List, Optional
import torch

from src.utils.hydra_resolvers import register_hydra_resolvers
from src.utils.wandb_utils import get_wandb_status
from src.utils.console_beautifier import get_console_beautifier

# 注册自定义的hydra解析器，必须在 @hydra.main 之前调用
register_hydra_resolvers()

@hydra.main(version_base=None, config_path="../configs", config_name="config.yaml")
def main(cfg: DictConfig) -> Optional[float]:
    """
    DL_Lightning 训练入口.
    
    Args:
        cfg (DictConfig): Hydra配置对象.
    """
    # 获取控制台美化器
    beautifier = get_console_beautifier()

    # 打印美化的启动横幅
    beautifier.print_startup_banner(cfg)

    # 打印WandB状态信息
    if "wandb" in cfg:
        try:
            wandb_status = get_wandb_status(cfg)
            beautifier.print_wandb_status(wandb_status)
        except Exception as e:
            beautifier.print_error(e, "WandB状态检查")

    # 如果需要查看详细配置，可以取消注释下面的代码
    # print("------ Hydra 配置信息 ------")
    # print(OmegaConf.to_yaml(cfg))
    # print("--------------------------")

    # --- 1. 实例化 DataModule ---
    beautifier.print_info("正在实例化 DataModule...", "📊")
    datamodule: pl.LightningDataModule = hydra.utils.instantiate(cfg.data)

    # --- 2. 实例化 Model (LightningModule) ---
    beautifier.print_info("正在实例化 Model...", "🤖")
    # 这里我们将 model, optimizer, scheduler, loss 的配置传入
    model: pl.LightningModule = hydra.utils.instantiate(
        cfg.model,
        optimizer_cfg=cfg.optimizer,
        scheduler_cfg=cfg.scheduler,
        loss_cfg=cfg.loss,
        _recursive_=False
    )

    # --- 3. 实例化 Callbacks ---
    beautifier.print_info("正在实例化 Callbacks...", "🔧")
    callbacks: List[pl.Callback] = []
    if "callbacks" in cfg:
        for callback_name, cb_conf in cfg.callbacks.items():
            if cb_conf is None:
                beautifier.print_info(f"跳过回调 (配置为null): {callback_name}", "⏭️")
                continue
            if "_target_" in cb_conf:
                # 在 fast_dev_run 模式下跳过 LearningRateMonitor
                if (callback_name == "learning_rate_monitor" and
                    cfg.trainer.get("fast_dev_run", False)):
                    beautifier.print_info(f"跳过回调 (fast_dev_run模式): {callback_name}", "⏭️")
                    continue

                callbacks.append(hydra.utils.instantiate(cb_conf))
                beautifier.print_info(f"添加回调: {callback_name}", "✅")
    
    # --- 4. 实例化 Logger ---
    beautifier.print_info("正在实例化 Logger...", "📝")
    loggers: List[Logger] = []

    # 在 fast_dev_run 模式下禁用 logger
    if cfg.trainer.get("fast_dev_run", False):
        beautifier.print_info("跳过Logger (fast_dev_run模式)", "⏭️")
    elif "logger" in cfg:
        # 检查 cfg.logger 的类型
        # 如果它不是一个字典（例如，只有一个logger时，它可能不是一个map），我们将它视为单个logger配置
        if not isinstance(cfg.logger, DictConfig):
             # 这是一个兼容性修正，以处理只有一个logger的情况
             if "_target_" in cfg.logger:
                 loggers.append(hydra.utils.instantiate(cfg.logger))
                 beautifier.print_info(f"添加Logger: {type(loggers[-1]).__name__}", "✅")
        else:
            # 支持多个logger的情况 (wandb.yaml, tensorboard.yaml, etc.)
            for logger_name, lg_conf in cfg.logger.items():
                if isinstance(lg_conf, DictConfig) and "_target_" in lg_conf:
                    logger_instance = hydra.utils.instantiate(lg_conf)
                    if logger_instance is not None:  # WandB可能在disabled模式下返回None
                        loggers.append(logger_instance)
                        beautifier.print_info(f"添加Logger: {logger_name}", "✅")
                    else:
                        beautifier.print_info(f"跳过Logger (已禁用): {logger_name}", "⏭️")

    # --- 5. 实例化 Trainer ---
    beautifier.print_info("正在实例化 Trainer...", "🏋️")
    trainer: pl.Trainer = hydra.utils.instantiate(
        cfg.trainer, callbacks=callbacks, logger=loggers
    )

    # --- 6. 开始训练 ---
    print("--> 开始训练!")
    trainer.fit(model=model, datamodule=datamodule)

    # --- 7. 训练结束 ---
    # 获取并返回一个关键指标，可用于HPO
    val_iou = trainer.callback_metrics.get("val/iou", 0.0)
    print(f"训练结束. 最终 val/iou: {val_iou}")
    
    # 确保返回的是 float 类型
    if isinstance(val_iou, torch.Tensor):
        return val_iou.item()
        
    return val_iou

if __name__ == "__main__":
    main()
