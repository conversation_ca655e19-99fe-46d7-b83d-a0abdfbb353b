#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试搜索空间文件的使用情况
检查 search_spaces.py 文件是否在项目中被实际使用
"""

import ast
import os
import sys
from pathlib import Path
from typing import List, Dict, Set


def find_python_files(directory: Path) -> List[Path]:
    """查找目录中的所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过一些不需要检查的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'outputs']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files


def find_imports_in_file(file_path: Path) -> Set[str]:
    """查找文件中的所有导入"""
    imports = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析AST
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.add(f"{node.module}.{alias.name}")
                        
        # 也检查字符串中的引用
        search_terms = [
            'search_spaces',
            'get_optimizer_search_space',
            'get_scheduler_search_space',
            'OPTIMIZER_SEARCH_SPACES',
            'SCHEDULER_SEARCH_SPACES',
            'create_optimizer_tune_config',
            'create_scheduler_tune_config'
        ]
        
        for term in search_terms:
            if term in content:
                imports.add(f"STRING_REFERENCE:{term}")
                
    except Exception as e:
        print(f"解析文件失败 {file_path}: {e}")
    
    return imports


def analyze_search_spaces_usage():
    """分析搜索空间文件的使用情况"""
    print("🔍 分析搜索空间文件使用情况")
    print("=" * 60)
    
    project_root = Path(__file__).parent.parent
    
    # 搜索空间文件
    search_space_files = [
        project_root / "src/optimizers/search_spaces.py",
        project_root / "src/schedulers/search_spaces.py"
    ]
    
    # 检查文件是否存在
    print("📁 搜索空间文件检查:")
    for file in search_space_files:
        if file.exists():
            print(f"   ✅ {file.relative_to(project_root)}")
        else:
            print(f"   ❌ {file.relative_to(project_root)} (不存在)")
    
    # 查找所有Python文件
    python_files = find_python_files(project_root)
    print(f"\n📊 扫描 {len(python_files)} 个Python文件...")
    
    # 搜索相关的导入和引用
    search_terms = {
        'search_spaces': [],
        'get_optimizer_search_space': [],
        'get_scheduler_search_space': [],
        'OPTIMIZER_SEARCH_SPACES': [],
        'SCHEDULER_SEARCH_SPACES': [],
        'create_optimizer_tune_config': [],
        'create_scheduler_tune_config': [],
        'OPTIMIZER_COMBINATIONS': [],
        'SCHEDULER_COMBINATIONS': [],
        'JOINT_SEARCH_SPACES': []
    }
    
    # 扫描所有文件
    for file_path in python_files:
        # 跳过搜索空间文件本身
        if file_path.name == 'search_spaces.py':
            continue
            
        imports = find_imports_in_file(file_path)
        
        for import_item in imports:
            for term in search_terms:
                if term in import_item:
                    search_terms[term].append({
                        'file': file_path.relative_to(project_root),
                        'import': import_item
                    })
    
    # 报告结果
    print(f"\n📋 使用情况报告:")
    print("-" * 60)
    
    total_usages = 0
    for term, usages in search_terms.items():
        if usages:
            print(f"\n🔍 {term}:")
            for usage in usages:
                print(f"   📄 {usage['file']}")
                print(f"      └─ {usage['import']}")
            total_usages += len(usages)
        else:
            print(f"\n❌ {term}: 未找到使用")
    
    print(f"\n📊 总结:")
    print(f"   总使用次数: {total_usages}")
    print(f"   使用的术语: {sum(1 for usages in search_terms.values() if usages)}/{len(search_terms)}")
    
    if total_usages == 0:
        print(f"\n⚠️ 警告: 搜索空间文件似乎没有被实际使用!")
        print(f"   这些文件可能是:")
        print(f"   1. 未完成的功能")
        print(f"   2. 遗留代码")
        print(f"   3. 计划中但未实现的功能")
    else:
        print(f"\n✅ 搜索空间文件正在被使用")
    
    return search_terms


def check_config_files():
    """检查配置文件中的搜索空间使用"""
    print(f"\n🔍 检查配置文件中的搜索空间引用")
    print("-" * 60)
    
    project_root = Path(__file__).parent.parent
    config_dir = project_root / "configs"
    
    if not config_dir.exists():
        print("❌ configs目录不存在")
        return
    
    # 查找所有YAML文件
    yaml_files = []
    for root, dirs, files in os.walk(config_dir):
        for file in files:
            if file.endswith(('.yaml', '.yml')):
                yaml_files.append(Path(root) / file)
    
    print(f"📊 扫描 {len(yaml_files)} 个配置文件...")
    
    search_terms = [
        'search_spaces',
        'optimizer_search',
        'scheduler_search',
        'hpo',
        'tune',
        'ray'
    ]
    
    found_references = []
    
    for yaml_file in yaml_files:
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for term in search_terms:
                if term in content.lower():
                    found_references.append({
                        'file': yaml_file.relative_to(project_root),
                        'term': term
                    })
        except Exception as e:
            print(f"读取文件失败 {yaml_file}: {e}")
    
    if found_references:
        print(f"\n✅ 找到配置文件引用:")
        for ref in found_references:
            print(f"   📄 {ref['file']} -> {ref['term']}")
    else:
        print(f"\n❌ 配置文件中未找到搜索空间相关引用")


def check_scripts_usage():
    """检查脚本文件中的使用情况"""
    print(f"\n🔍 检查脚本文件中的搜索空间使用")
    print("-" * 60)
    
    project_root = Path(__file__).parent.parent
    scripts_dir = project_root / "scripts"
    
    if not scripts_dir.exists():
        print("❌ scripts目录不存在")
        return
    
    script_files = list(scripts_dir.glob("*.py"))
    print(f"📊 扫描 {len(script_files)} 个脚本文件...")
    
    for script_file in script_files:
        print(f"\n📄 {script_file.name}:")
        
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查搜索空间相关的导入和使用
            search_indicators = [
                'search_spaces',
                'tune.loguniform',
                'tune.choice',
                'tune.uniform',
                'ray.tune',
                'get_optimizer_search_space',
                'get_scheduler_search_space'
            ]
            
            found = []
            for indicator in search_indicators:
                if indicator in content:
                    found.append(indicator)
            
            if found:
                print(f"   ✅ 找到: {', '.join(found)}")
            else:
                print(f"   ❌ 未找到搜索空间相关代码")
                
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")


def main():
    """主函数"""
    print("搜索空间文件使用情况分析")
    print("=" * 70)
    
    # 分析Python文件中的使用
    search_terms = analyze_search_spaces_usage()
    
    # 检查配置文件
    check_config_files()
    
    # 检查脚本文件
    check_scripts_usage()
    
    # 最终结论
    print(f"\n" + "=" * 70)
    print("📋 最终结论")
    print("=" * 70)
    
    total_usages = sum(len(usages) for usages in search_terms.values())
    
    if total_usages == 0:
        print(f"\n❌ 搜索空间文件未被使用")
        print(f"   建议:")
        print(f"   1. 如果这些是计划中的功能，请实现使用逻辑")
        print(f"   2. 如果不再需要，可以考虑删除这些文件")
        print(f"   3. 如果是示例代码，请移动到examples目录")
    else:
        print(f"\n✅ 搜索空间文件正在被使用")
        print(f"   使用次数: {total_usages}")
        print(f"   这些文件是项目的有效组成部分")
    
    return total_usages > 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
