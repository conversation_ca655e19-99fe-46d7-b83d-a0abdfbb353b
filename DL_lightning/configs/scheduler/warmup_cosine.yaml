# @package _global_

# Warmup + Cosine Annealing组合调度器配置
# 适用场景：大模型训练、Transformer架构
# 推荐用于：需要warmup的现代架构

_target_: src.schedulers.warmup_scheduler.WarmupCosineAnnealingLR
warmup_epochs: 10  # warmup的epoch数
max_epochs: 100  # 总的训练epoch数
warmup_start_lr: 1e-6  # warmup开始的学习率
eta_min: 1e-6  # 最小学习率

# 使用说明：
# - 结合warmup和cosine annealing的优势
# - warmup_epochs: 线性增长的epoch数
# - warmup_start_lr: warmup阶段的起始学习率
# - 适合大模型和Transformer架构
# - 需要实现自定义的WarmupCosineAnnealingLR类
