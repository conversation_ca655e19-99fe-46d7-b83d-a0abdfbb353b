# 多尺度训练专用学习率调度器配置示例
# 移除文件名中的 "EXAMPLE_" 前缀来启用此配置

_target_: src.schedulers.examples.multiscale_scheduler.MultiScaleScheduler

# 基础参数
base_epochs: 100            # 基础训练轮数
warmup_epochs: 5            # 预热轮数
min_lr_factor: 0.01         # 最小学习率因子

# 尺度调度配置
scale_schedule:
  '256': 0.8                # 小尺度，降低学习率
  '512': 1.0                # 标准尺度
  '768': 1.2                # 大尺度，提高学习率
  '1024': 1.5               # 超大尺度，显著提高学习率

# 使用方式:
# python scripts/train.py scheduler=multiscale_scheduler

# 参数覆盖示例:
# python scripts/train.py scheduler=multiscale_scheduler scheduler.base_epochs=200 scheduler.warmup_epochs=10
