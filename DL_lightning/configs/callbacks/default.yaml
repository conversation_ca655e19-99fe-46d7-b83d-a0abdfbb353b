# 默认启用的回调列表

model_checkpoint:
  _target_: lightning.pytorch.callbacks.ModelCheckpoint
  
  # dirpath不再是写死的，而是动态计算出来的！
  # 它会调用 os.path.join(${get_run_dir:}, "checkpoints")
  dirpath: ${path_join:${get_run_dir:}, "checkpoints"}
  
  filename: "epoch_{epoch:03d}-iou_{val/iou:.4f}"
  monitor: "val/iou"
  mode: "max"
  save_top_k: 1
  auto_insert_metric_name: false
  save_last: true

learning_rate_monitor:
  _target_: lightning.pytorch.callbacks.LearningRateMonitor
  logging_interval: "step"

rich_model_summary:
  _target_: lightning.pytorch.callbacks.RichModelSummary

rich_progress:
  _target_: src.callbacks.rich_progress_callback.RichProgressCallback
  refresh_rate: 10
  enable_system_monitor: true
  show_system_stats: true
  leave_progress: true

# 之后我们会在这里添加自定义的WandB可视化回调
# log_segmentation_masks:
#   _target_: src.callbacks.wandb_callbacks.LogSegmentationMasksCallback
#   num_samples: 8
