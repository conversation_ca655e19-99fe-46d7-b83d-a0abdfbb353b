# Hydra + WandB + Ray Tune 集成配置示例
# 完全无需注册器的超参数优化配置

defaults:
  - _self_

# 运行模式: single (单次训练) 或 hpo (超参数优化)
mode: hpo

# 项目配置
project_name: "dl_lightning_hpo_demo"
num_classes: 7

# 模型配置 (单次运行的默认配置)
model:
  _target_: src.models.segmentation.UNet
  in_channels: 3
  pretrained: true
  # num_classes 会在运行时注入

# 优化器配置 (单次运行的默认配置)
optimizer:
  _target_: src.optimizers.standard.AdamW
  lr: 1e-3
  weight_decay: 1e-2
  betas: [0.9, 0.999]

# 调度器配置 (单次运行的默认配置)
scheduler:
  _target_: src.schedulers.standard.CosineAnnealingLR
  T_max: 100
  eta_min: 1e-6

# 损失函数配置
loss:
  _target_: torch.nn.CrossEntropyLoss
  ignore_index: 255

# 数据配置
data:
  _target_: src.data.suide_datamodule.SuiDeDataModule
  dataset_config:
    data_dir: "data/Data_SRC/Dataset_v2.2"
    class_info_path: "data/Data_SRC/Dataset_v2.2/metadata/class_info.json"
    training_level: 2
    ignore_index: 255
    scale_weights:
      "1_1": 0.7
      "1_2": 0.2
      "1_0.5": 0.1
  transform_config: null
  dataloader_config:
    batch_size: 16
    num_workers: 4
    pin_memory: true

# 训练器配置
trainer:
  max_epochs: 50
  accelerator: auto
  devices: auto
  precision: 16-mixed
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1
  check_val_every_n_epoch: 1

# 超参数优化配置
hpo:
  num_samples: 20        # 尝试的配置数量
  max_epochs: 50         # 每个试验的最大epoch数
  grace_period: 10       # ASHA调度器的宽限期
  
  # 搜索空间在代码中定义，支持：
  # - 不同模型架构 (UNet, DeepLabV3Plus)
  # - 不同优化器 (AdamW, Lion)
  # - 不同调度器 (CosineAnnealingLR, OneCycleLR)
  # - 学习率、权重衰减等超参数
  # - 批量大小、训练轮数等训练参数

# WandB配置
wandb:
  entity: null          # 你的WandB用户名或团队名
  project: ${project_name}
  tags:
    - hydra
    - ray_tune
    - segmentation
  notes: "Hydra + WandB + Ray Tune 集成示例"
