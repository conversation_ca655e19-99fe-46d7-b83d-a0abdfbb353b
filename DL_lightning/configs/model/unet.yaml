_target_: src.modules.segmentation_module.SegmentationModule
ignore_index: 255
loss_cfg: ${loss}
model_name: unet
model_params:
  bilinear: true
  features:
  - 64
  - 128
  - 256
  - 512
  in_channels: 3
  pretrained: false
name: UNet
num_classes: ${data.dataset_config.num_classes}
optimizer_cfg:
  _partial_: true
  _target_: torch.optim.Adam
  betas:
  - 0.9
  - 0.999
  lr: 0.001
  weight_decay: 1e-4
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.StepLR
  gamma: 0.1
  step_size: 30
