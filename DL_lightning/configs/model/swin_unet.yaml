_target_: src.modules.segmentation_module.SegmentationModule
ignore_index: 255
loss_cfg: ${loss}
model_name: swin_unet
model_params:
  depths:
  - 2
  - 2
  - 6
  - 2
  img_size: 512
  in_channels: 3
  pretrained: true
name: Swin-UNet
num_classes: ${data.dataset_config.num_classes}
optimizer_cfg:
  _partial_: true
  _target_: torch.optim.AdamW
  betas:
  - 0.9
  - 0.999
  lr: 0.0001
  weight_decay: 0.05
scheduler_cfg:
  T_max: 150
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  eta_min: 1e-7
