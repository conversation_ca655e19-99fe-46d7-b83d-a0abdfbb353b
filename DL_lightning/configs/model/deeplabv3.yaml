_target_: src.modules.segmentation_module.SegmentationModule
ignore_index: 255
loss_cfg: ${loss}
model_name: deeplabv3plus
model_params:
  backbone: resnet50
  in_channels: 3
  pretrained: true
name: DeepLabV3Plus
num_classes: ${data.dataset_config.num_classes}
optimizer_cfg:
  _partial_: true
  _target_: torch.optim.AdamW
  betas:
  - 0.9
  - 0.999
  lr: 0.001
  weight_decay: 0.01
scheduler_cfg:
  T_max: 100
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  eta_min: 1e-6
