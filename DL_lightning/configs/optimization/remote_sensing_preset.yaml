# 遥感图像分割专用优化预设
# 针对遥感数据特点优化的优化器和调度器组合

# 优化器配置 - 使用遥感专用AdamW
optimizer:
  _target_: src.optimizers.examples.remote_sensing_adamw.RemoteSensingAdamW
  lr: 1e-3
  betas: [0.9, 0.999]
  eps: 1e-8
  weight_decay: 1e-2
  gradient_clip: 1.0
  adaptive_wd: true
  scale_aware: true

# 调度器配置 - 使用多尺度调度器
scheduler:
  _target_: src.schedulers.examples.multiscale_scheduler.MultiScaleScheduler
  base_epochs: 100
  warmup_epochs: 5
  min_lr_factor: 0.01
  scale_schedule:
    '256': 0.8    # 小尺度，降低学习率
    '512': 1.0    # 标准尺度
    '768': 1.2    # 大尺度，提高学习率
    '1024': 1.5   # 超大尺度，显著提高学习率

# 使用方式:
# python scripts/train.py optimization=remote_sensing_preset

# 参数覆盖示例:
# python scripts/train.py optimization=remote_sensing_preset optimizer.lr=0.01 scheduler.base_epochs=200
