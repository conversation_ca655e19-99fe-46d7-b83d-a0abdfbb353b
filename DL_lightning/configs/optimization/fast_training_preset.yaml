# 快速训练预设
# 适用于快速实验和原型开发的优化器和调度器组合

# 优化器配置 - 使用标准AdamW，较高学习率
optimizer:
  _target_: torch.optim.AdamW
  lr: 2e-3
  betas: [0.9, 0.999]
  eps: 1e-8
  weight_decay: 5e-3

# 调度器配置 - 使用余弦退火，快速收敛
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  T_max: 50
  eta_min: 1e-6

# 使用方式:
# python scripts/train.py optimization=fast_training_preset trainer.max_epochs=50

# 适用场景:
# - 快速验证模型架构
# - 数据集初步测试
# - 超参数搜索的基线
