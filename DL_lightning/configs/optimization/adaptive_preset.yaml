# 自适应训练预设
# 使用自适应调度器，根据验证损失动态调整学习率

# 优化器配置 - 使用标准AdamW
optimizer:
  _target_: torch.optim.AdamW
  lr: 1e-3
  betas: [0.9, 0.999]
  eps: 1e-8
  weight_decay: 1e-2

# 调度器配置 - 使用自适应余弦调度器
scheduler:
  _target_: src.schedulers.examples.multiscale_scheduler.AdaptiveCosineScheduler
  T_max: 100
  eta_min: 1e-6
  adaptation_factor: 0.1
  patience: 5

# 使用方式:
# python scripts/train.py optimization=adaptive_preset

# 特点:
# - 根据验证损失自动调整学习率
# - 在损失平台期时加速衰减
# - 适合不确定最优超参数的场景
