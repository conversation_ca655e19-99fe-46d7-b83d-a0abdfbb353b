# 优化器和调度器组合预设

## 📋 用途说明

此目录包含预定义的优化器和调度器组合配置，用于不同的训练场景。这些预设提供了常用的优化策略，可以直接使用或作为自定义配置的起点。

## 🔧 可用预设

### 1. 遥感分割专用预设 (`remote_sensing_preset.yaml`)
- **优化器**: 遥感专用AdamW (自适应梯度裁剪和权重衰减)
- **调度器**: 多尺度调度器 (根据图像尺度动态调整学习率)
- **适用场景**: 遥感图像分割任务，多尺度训练

### 2. 快速训练预设 (`fast_training_preset.yaml`)
- **优化器**: AdamW (较高学习率)
- **调度器**: 余弦退火 (快速收敛)
- **适用场景**: 快速实验，原型开发，初步测试

### 3. 稳定训练预设 (`stable_training_preset.yaml`)
- **优化器**: AdamW (保守参数)
- **调度器**: 带重启的余弦退火
- **适用场景**: 生产环境，最终模型训练，长时间稳定训练

### 4. 自适应训练预设 (`adaptive_preset.yaml`)
- **优化器**: AdamW
- **调度器**: 自适应余弦调度器 (根据验证损失动态调整)
- **适用场景**: 不确定最优超参数的场景，需要自动调整学习率

## 🚀 使用方式

### 基本用法
```bash
# 使用遥感分割专用预设
python scripts/train.py optimization=remote_sensing_preset

# 使用快速训练预设
python scripts/train.py optimization=fast_training_preset

# 使用稳定训练预设
python scripts/train.py optimization=stable_training_preset

# 使用自适应训练预设
python scripts/train.py optimization=adaptive_preset
```

### 参数覆盖
```bash
# 覆盖优化器参数
python scripts/train.py optimization=remote_sensing_preset optimizer.lr=0.01

# 覆盖调度器参数
python scripts/train.py optimization=stable_training_preset scheduler.T_0=10
```

## 🔧 创建自定义预设

1. 复制现有预设作为模板
2. 修改优化器和调度器配置
3. 保存为新的YAML文件 (例如 `custom_preset.yaml`)
4. 使用新预设: `python scripts/train.py optimization=custom_preset`

## 📚 参考资源

- 优化器文档: `src/optimizers/README.md`
- 调度器文档: `src/schedulers/README.md`
- 工厂函数: `src/optimizers/factory.py` 和 `src/schedulers/factory.py`
