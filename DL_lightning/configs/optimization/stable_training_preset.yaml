# 稳定训练预设
# 适用于生产环境和最终模型训练的保守优化器和调度器组合

# 优化器配置 - 使用标准AdamW，保守参数
optimizer:
  _target_: torch.optim.AdamW
  lr: 5e-4
  betas: [0.9, 0.999]
  eps: 1e-8
  weight_decay: 1e-2

# 调度器配置 - 使用带重启的余弦退火
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  T_0: 20
  T_mult: 2
  eta_min: 1e-7

# 使用方式:
# python scripts/train.py optimization=stable_training_preset trainer.max_epochs=200

# 适用场景:
# - 生产环境训练
# - 最终模型优化
# - 长时间稳定训练
