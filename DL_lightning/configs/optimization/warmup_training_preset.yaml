# 预热训练预设
# 适用于大批量训练和复杂模型的预热策略

# 优化器配置 - 使用标准AdamW
optimizer:
  _target_: torch.optim.AdamW
  lr: 1e-3
  betas: [0.9, 0.999]
  eps: 1e-8
  weight_decay: 1e-2

# 调度器配置 - 使用带预热的余弦退火
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  T_max: 100
  eta_min: 1e-6
  
  # 预热参数
  warmup_epochs: 5
  warmup_start_lr: 1e-6

# 使用方式:
# python scripts/train.py optimization=warmup_training_preset

# 适用场景:
# - 大批量训练（batch_size >= 64）
# - 复杂模型训练（参数量 > 10M）
# - 需要稳定收敛的场景

# 参数覆盖示例:
# python scripts/train.py optimization=warmup_training_preset scheduler.warmup_epochs=10 optimizer.lr=2e-3
