# 遥感图像分割专用 AdamW 优化器配置示例
# 移除文件名中的 "EXAMPLE_" 前缀来启用此配置

_target_: src.optimizers.examples.remote_sensing_adamw.RemoteSensingAdamW

# 基础 AdamW 参数
lr: 1e-3                    # 学习率
betas: [0.9, 0.999]         # Adam beta 参数
eps: 1e-8                   # 数值稳定性参数
weight_decay: 1e-2          # 权重衰减

# 遥感分割特化参数
gradient_clip: 1.0          # 梯度裁剪阈值
adaptive_wd: true           # 启用自适应权重衰减
scale_aware: true           # 启用多尺度感知

# 使用方式:
# python scripts/train.py optimizer=remote_sensing_adamw

# 参数覆盖示例:
# python scripts/train.py optimizer=remote_sensing_adamw optimizer.lr=0.01 optimizer.gradient_clip=2.0
