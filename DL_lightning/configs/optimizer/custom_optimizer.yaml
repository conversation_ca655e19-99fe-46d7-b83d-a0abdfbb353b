# 自定义优化器配置示例
# 如果需要使用自定义优化器，可以参考此文件

# 示例1: 使用RMSprop优化器
# _target_: torch.optim.RMSprop
# lr: 0.01
# momentum: 0.9
# weight_decay: 1e-4

# 示例2: 使用Adagrad优化器  
# _target_: torch.optim.Adagrad
# lr: 0.01
# weight_decay: 1e-4

# 示例3: 自定义优化器类（需要先实现）
# _target_: src.custom.optimizers.CustomOptimizer
# lr: 0.001
# custom_param: 0.5

# 注意：
# 1. 移除文件名中的 "EXAMPLE_" 前缀来启用配置
# 2. 确保 _target_ 指向的类存在且可导入
# 3. 参数名称必须与目标类的构造函数参数匹配
