# HPO专用配置文件 - 独立配置以避免嵌套冲突
# 基础训练配置
project_name: "SuiDe_RemoteSensing"
experiment_name: "baseline"
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# W&B 相关配置
wandb:
  project: ${project_name}
  name: ${run_name}
  tags: ["baseline", "unet"]
  notes: "A baseline run for segmentation."

# 训练器配置
trainer:
  _target_: lightning.pytorch.Trainer
  accelerator: auto
  devices: auto
  precision: 16-mixed
  max_epochs: 50
  check_val_every_n_epoch: 1
  log_every_n_steps: 10
  enable_progress_bar: true

# 模型配置
model:
  _target_: src.modules.segmentation_module.SegmentationModule
  optimizer_cfg: ${optimizer}
  scheduler_cfg: ${scheduler}
  loss_cfg: ${loss}
  num_classes: ${data.dataset_config.num_classes}
  architecture:
    _target_: src.models.architectures.unet.UNet
    n_channels: 3
    n_classes: ${data.dataset_config.num_classes}
    bilinear: true

# 数据配置
data:
  _target_: src.data.remote_sensing_datamodule.RemoteSensingDataModule
  data_dir: ${oc.env:DATA_DIR, ./data/}
  image_size: 256
  num_classes: 5
  num_samples_train: 1000
  num_samples_val: 200
  num_samples_test: 200
  batch_size: 32
  num_workers: 4
  pin_memory: true

# 损失函数配置
loss:
  _target_: src.losses.dice_loss.CEDiceLoss
  ce_weight: 0.5
  dice_weight: 0.5
  class_weights: null

# 优化器配置
optimizer:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 0.0001
  weight_decay: 0.01

# 调度器配置
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  _partial_: true
  T_max: 100
  eta_min: 1e-6

# 日志记录器配置
logger:
  _target_: lightning.pytorch.loggers.WandbLogger
  project: ${wandb.project}
  name: ${wandb.name}
  save_dir: ${get_run_dir:}
  log_model: false
  tags: ${wandb.tags}
  notes: ${wandb.notes}

# 回调函数配置
callbacks:
  model_checkpoint:
    _target_: lightning.pytorch.callbacks.ModelCheckpoint
    dirpath: ${path_join:${get_run_dir:}, "checkpoints"}
    filename: "epoch_{epoch:03d}-iou_{val/iou:.4f}"
    monitor: "val/iou"
    mode: "max"
    save_top_k: 1
    auto_insert_metric_name: false
    save_last: true
  learning_rate_monitor:
    _target_: lightning.pytorch.callbacks.LearningRateMonitor
    logging_interval: step
  rich_model_summary:
    _target_: lightning.pytorch.callbacks.RichModelSummary

# HPO专用配置
hpo:
  experiment_name: "unet_baseline_hpo"
  run_params:
    name: "unet_hpo_basic"
    num_samples: 2
    local_dir: "${get_project_root:}/experiments_output/ray_results"
  ray_init_args:
    _target_: ray.init
    num_cpus: 4
    num_gpus: 1
  resources_per_trial:
    use_gpu: true
    CPU: 1
    GPU: 1
  scheduler:
    _target_: ray.tune.schedulers.ASHAScheduler
    metric: "val_iou"
    mode: "max"
    max_t: 20
    grace_period: 5
    reduction_factor: 2
  search_space:
    lr:
      min: 1e-5
      max: 1e-2
    batch_size:
      - 16
      - 32
