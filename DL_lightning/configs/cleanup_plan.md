# 配置文件清理计划

## 🗑️ 需要删除的多余配置文件

### 1. HPO配置重复
```bash
# 删除根目录下的重复HPO配置
rm configs/hpo_example.yaml
rm configs/hpo_advanced.yaml
```
**原因**: 与 `configs/hpo/` 目录下的配置功能重复，且结构不一致。

### 2. 示例配置文件
```bash
# 删除EXAMPLE前缀的示例文件
rm configs/optimizer/EXAMPLE_remote_sensing_adamw.yaml
rm configs/optimizer/EXAMPLE_custom_optimizer.yaml
rm configs/scheduler/EXAMPLE_multiscale_scheduler.yaml
```
**原因**: 这些是示例文件，与正式配置重复。

### 3. 重复的WandB配置
```bash
# 删除冗余的WandB模式配置
rm configs/logger/wandb_online.yaml
rm configs/logger/wandb_offline.yaml  
rm configs/logger/wandb_disabled.yaml
```
**原因**: 可以通过参数覆盖 `wandb.mode` 实现，无需单独文件。

### 4. 重复的调度器配置
```bash
# 删除重复的warmup配置
rm configs/scheduler/warmup_cosine.yaml
```
**原因**: 与 `cosine_with_warmup.yaml` 功能重复。

## 📝 配置内容优化

### 1. 统一WandB配置
保留 `configs/logger/wandb.yaml` 作为默认配置，通过参数覆盖实现模式切换：
```bash
# 在线模式
python scripts/train.py wandb.mode=online

# 离线模式  
python scripts/train.py wandb.mode=offline

# 禁用模式
python scripts/train.py wandb.mode=disabled
```

### 2. 清理重复键值
根据配置冲突报告，以下键值在多个文件中重复：
- `project_name`
- `experiment_name` 
- `wandb.*` 相关配置
- 数据增强参数

**建议**: 将公共配置提取到默认配置中，避免重复定义。

## ✅ 清理后的配置结构

```
configs/
├── config.yaml                    # 主配置文件
├── trainer/default.yaml          # 训练器配置
├── model/                         # 模型配置
│   ├── deeplabv3.yaml
│   ├── unet.yaml
│   ├── unetpp.yaml
│   └── swin_unet.yaml
├── data/                          # 数据配置
│   ├── suide.yaml
│   └── loveda.yaml
├── loss/                          # 损失函数配置
│   ├── combined.yaml
│   ├── cross_entropy.yaml
│   ├── dice.yaml
│   ├── focal.yaml
│   └── lovasz.yaml
├── optimizer/                     # 优化器配置
│   ├── adamw.yaml
│   ├── adam.yaml
│   ├── sgd.yaml
│   ├── rmsprop.yaml
│   ├── adamax.yaml
│   ├── remote_sensing_adamw.yaml
│   └── custom_optimizer.yaml
├── scheduler/                     # 调度器配置
│   ├── cosine.yaml
│   ├── cosine_with_warmup.yaml
│   ├── step_with_warmup.yaml
│   ├── poly.yaml
│   ├── polynomial.yaml
│   ├── reduce_on_plateau.yaml
│   └── multiscale_scheduler.yaml
├── logger/                        # 日志配置
│   └── wandb.yaml                 # 统一WandB配置
├── callbacks/                     # 回调配置
│   ├── default.yaml
│   └── rich_progress.yaml
├── optimization/                  # 优化预设
│   ├── remote_sensing_preset.yaml
│   ├── fast_training_preset.yaml
│   ├── stable_training_preset.yaml
│   ├── adaptive_preset.yaml
│   └── warmup_training_preset.yaml
├── experiment/                    # 实验配置
│   ├── baseline_unet.yaml
│   ├── hpo_search_space.yaml
│   └── segmentation_best_practices.yaml
└── hpo/                          # HPO专用配置
    ├── tune_basic.yaml
    └── optimizer_scheduler_search.yaml
```

## 🎯 清理效果

- **删除文件数**: 7个重复/多余配置文件
- **减少配置冲突**: 消除重复键值定义
- **提高一致性**: 统一配置结构和命名规范
- **简化维护**: 减少配置文件数量，便于管理

## 🚀 执行清理

执行以下命令进行清理：
```bash
cd DL_lightning/configs

# 删除重复的HPO配置
rm hpo_example.yaml hpo_advanced.yaml

# 删除示例配置文件
rm optimizer/EXAMPLE_*.yaml
rm scheduler/EXAMPLE_*.yaml

# 删除重复的WandB配置
rm logger/wandb_online.yaml logger/wandb_offline.yaml logger/wandb_disabled.yaml

# 删除重复的调度器配置
rm scheduler/warmup_cosine.yaml
```
