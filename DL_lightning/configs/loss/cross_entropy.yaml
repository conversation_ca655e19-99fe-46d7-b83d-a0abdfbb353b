# 交叉熵损失函数配置 - Lightning版本
# 适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+

_target_: src.losses.cross_entropy_loss.CrossEntropyLoss

# 类别数量
num_classes: ${data.dataset_config.num_classes}

# 忽略的标签索引
ignore_index: 255

# 初始类别权重（可选）
weight: null

# 损失聚合方式 ('mean', 'sum', 'none')
reduction: 'mean'

# 是否使用动态类别权重
use_dynamic_weights: true

# 权重计算方法 ('inverse', 'sqrt_inverse', 'log_inverse')
weight_method: 'inverse'

# 标签平滑系数
label_smoothing: 0.0
