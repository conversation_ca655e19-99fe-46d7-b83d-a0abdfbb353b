# 组合损失函数配置 - Lightning版本
# 适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+

_target_: src.losses.combined_loss.CombinedLoss

# 各损失函数的权重
loss_weights:
  ce: 0.4    # 交叉熵损失权重
  dice: 0.4  # Dice损失权重
  focal: 0.2 # Focal损失权重

# 忽略的标签索引
ignore_index: 255

# 是否使用动态类别权重
use_dynamic_weights: true

# 类别数量
num_classes: ${data.dataset_config.num_classes}

# 交叉熵损失特定参数
ce_params:
  weight_method: 'inverse'  # 权重计算方法: 'inverse', 'sqrt_inverse', 'log_inverse'
  label_smoothing: 0.1      # 标签平滑系数

# Dice损失特定参数
dice_params:
  smooth: 1.0               # 平滑参数
  per_class: false          # 是否返回每个类别的损失
  square: false             # 是否对预测和目标进行平方

# Focal损失特定参数
focal_params:
  alpha: 0.25               # 平衡因子
  gamma: 2.0                # 聚焦参数
  weight_method: 'inverse'  # 权重计算方法
