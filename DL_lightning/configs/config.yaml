defaults:
  - _self_
  - trainer: default
  - model: deeplabv3  # 默认使用DeepLabV3+模型
  - data: suide           # 使用SuiDe数据集配置
  - loss: combined        # 默认使用组合损失函数
  - optimizer: adamw      # 默认优化器
  - scheduler: cosine     # 默认调度器
  - logger: wandb
  - callbacks: default
  - experiment: null

# 全局设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "baseline"
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# W&B 相关配置 - 支持本地化和云端模式切换
wandb:
  # 基本配置
  project: ${project_name}
  name: ${run_name}
  tags: ["baseline", "${model.name}"]
  notes: "A baseline run for segmentation."

  # 模式配置 (online/offline/disabled/auto)
  # auto: 自动检测网络状况并选择最佳模式
  # online: 强制在线模式，数据实时同步到云端
  # offline: 强制离线模式，数据保存到本地，可后续同步
  # disabled: 禁用WandB，不记录任何数据
  mode: auto

  # 本地存储配置
  local_dir: null  # 如果为null，将使用Hydra输出目录下的wandb_local子目录

  # 高级配置
  log_model: false     # 是否记录模型文件
  silent: true         # 是否静默运行，减少控制台输出
  api_key: null        # WandB API密钥，建议通过环境变量设置

  # 网络配置
  timeout: 10          # 网络连接超时时间(秒)
  retry_attempts: 3    # 连接失败重试次数
