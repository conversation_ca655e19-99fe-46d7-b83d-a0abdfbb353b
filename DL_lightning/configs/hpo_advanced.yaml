# 高级配置驱动HPO - 搜索空间也在配置中定义
# 完全无需注册器，所有搜索空间都通过配置定义

defaults:
  - _self_

mode: hpo_advanced
project_name: "dl_lightning_advanced_hpo"
num_classes: 7

# 基础配置 (作为搜索的起点)
base_config:
  model:
    _target_: src.models.segmentation.UNet
    in_channels: 3
    pretrained: true
  
  optimizer:
    _target_: src.optimizers.standard.AdamW
    lr: 1e-3
    weight_decay: 1e-2
  
  scheduler:
    _target_: src.schedulers.standard.CosineAnnealingLR
    T_max: 100
  
  trainer:
    max_epochs: 50
    
  data:
    batch_size: 16

# 搜索空间定义 - 完全配置驱动
search_spaces:
  
  # 模型架构搜索空间
  model_search:
    type: "choice"
    choices:
      - _target_: src.models.segmentation.UNet
        in_channels: 3
        pretrained: true
      - _target_: src.models.segmentation.DeepLabV3Plus
        in_channels: 3
        pretrained: true
        backbone: "resnet50"
      - _target_: src.models.segmentation.UNetPlusPlus
        in_channels: 3
        pretrained: true
  
  # 优化器搜索空间
  optimizer_search:
    type: "choice"
    choices:
      - _target_: src.optimizers.standard.AdamW
        lr:
          type: "loguniform"
          lower: 1e-5
          upper: 1e-2
        weight_decay:
          type: "loguniform"
          lower: 1e-5
          upper: 1e-1
        betas:
          type: "choice"
          choices: [[0.9, 0.999], [0.9, 0.99], [0.95, 0.999]]
      
      - _target_: src.optimizers.advanced.Lion
        lr:
          type: "loguniform"
          lower: 1e-6
          upper: 1e-3
        weight_decay:
          type: "loguniform"
          lower: 1e-5
          upper: 1e-1
        betas:
          type: "choice"
          choices: [[0.9, 0.99], [0.95, 0.98]]
      
      - _target_: src.optimizers.standard.SGD
        lr:
          type: "loguniform"
          lower: 1e-4
          upper: 1e-1
        momentum:
          type: "uniform"
          lower: 0.8
          upper: 0.99
        weight_decay:
          type: "loguniform"
          lower: 1e-5
          upper: 1e-1
  
  # 调度器搜索空间
  scheduler_search:
    type: "choice"
    choices:
      - _target_: src.schedulers.standard.CosineAnnealingLR
        T_max:
          type: "choice"
          choices: [80, 100, 120]
        eta_min:
          type: "loguniform"
          lower: 1e-8
          upper: 1e-5
      
      - _target_: src.schedulers.advanced.OneCycleLR
        max_lr:
          type: "loguniform"
          lower: 1e-4
          upper: 1e-2
        total_steps: 1000  # 固定值
        pct_start:
          type: "uniform"
          lower: 0.2
          upper: 0.4
      
      - _target_: src.schedulers.standard.StepLR
        step_size:
          type: "choice"
          choices: [20, 30, 40]
        gamma:
          type: "uniform"
          lower: 0.05
          upper: 0.2
  
  # 训练参数搜索空间
  training_search:
    max_epochs:
      type: "choice"
      choices: [30, 50, 80]
    
    gradient_clip_val:
      type: "choice"
      choices: [0.5, 1.0, 2.0]
    
    accumulate_grad_batches:
      type: "choice"
      choices: [1, 2, 4]
  
  # 数据参数搜索空间
  data_search:
    batch_size:
      type: "choice"
      choices: [8, 16, 32]
    
    # 数据增强参数
    augmentation_strength:
      type: "uniform"
      lower: 0.1
      upper: 0.5

# HPO配置
hpo_config:
  num_samples: 50
  max_concurrent_trials: 4
  
  # 调度器配置
  scheduler:
    type: "asha"
    metric: "val_loss"
    mode: "min"
    max_t: 80
    grace_period: 15
    reduction_factor: 2
  
  # 搜索算法配置
  search_algorithm:
    type: "optuna"  # 或 "hyperopt", "bayesopt"
  
  # 早停配置
  stopper:
    type: "plateau"
    metric: "val_loss"
    mode: "min"
    patience: 10
    min_delta: 0.001

# WandB配置
wandb_config:
  project: ${project_name}
  entity: null
  tags:
    - advanced_hpo
    - config_driven
    - no_registry
  group: "hydra_raytune_integration"
  job_type: "hyperparameter_optimization"
  
  # 记录配置
  log_config: true
  log_code: true
  
  # 监控配置
  monitor_gym: false
  save_code: true
