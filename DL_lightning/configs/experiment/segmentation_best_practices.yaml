# @package _global_

# 语义分割任务最佳实践配置组合
# 基于Papers with Code和业界经验总结

defaults:
  - base_config
  - _self_

# 实验名称
experiment_name: "segmentation_best_practices"

# 不同架构的推荐配置组合
segmentation_configs:
  
  # 配置1: Transformer架构 (Swin-UNet, SegFormer等)
  transformer_setup:
    optimizer:
      _target_: torch.optim.AdamW
      _partial_: true
      lr: 1e-4
      betas: [0.9, 0.999]
      eps: 1e-08
      weight_decay: 0.05  # Transformer通常需要更强的正则化
      
    scheduler:
      _target_: src.schedulers.warmup_scheduler.WarmupCosineAnnealingLR
      warmup_epochs: 10
      max_epochs: ${trainer.max_epochs}
      warmup_start_lr: 1e-6
      eta_min: 1e-6
      
    # 推荐模型
    recommended_models: ["swin_unet", "segformer"]
    
  # 配置2: CNN架构 (DeepLabV3+, UNet等)
  cnn_setup:
    optimizer:
      _target_: torch.optim.SGD
      _partial_: true
      lr: 0.01
      momentum: 0.9
      weight_decay: 1e-4
      nesterov: true
      
    scheduler:
      _target_: torch.optim.lr_scheduler.PolynomialLR
      total_iters: ${trainer.max_epochs}
      power: 0.9
      
    # 推荐模型
    recommended_models: ["deeplabv3plus", "unet", "unetpp"]
    
  # 配置3: 混合架构 (现代CNN + 注意力机制)
  hybrid_setup:
    optimizer:
      _target_: torch.optim.AdamW
      _partial_: true
      lr: 5e-4
      betas: [0.9, 0.999]
      eps: 1e-08
      weight_decay: 0.01
      
    scheduler:
      _target_: torch.optim.lr_scheduler.CosineAnnealingLR
      T_max: ${trainer.max_epochs}
      eta_min: 1e-6
      
    # 推荐模型
    recommended_models: ["efficientnet_unet", "resnext_unet"]
    
  # 配置4: 轻量级模型 (移动端部署)
  lightweight_setup:
    optimizer:
      _target_: torch.optim.Adam
      _partial_: true
      lr: 0.001
      betas: [0.9, 0.999]
      eps: 1e-08
      weight_decay: 1e-4
      
    scheduler:
      _target_: torch.optim.lr_scheduler.StepLR
      step_size: 30
      gamma: 0.1
      
    # 推荐模型
    recommended_models: ["mobilenet_unet", "lightweight_deeplabv3"]

# 数据集特定的调整建议
dataset_specific_adjustments:
  
  # 遥感数据 (如您的SuiDe项目)
  remote_sensing:
    optimizer_adjustments:
      lr_multiplier: 0.5  # 遥感数据通常需要更小的学习率
      weight_decay_multiplier: 2.0  # 更强的正则化
    
    scheduler_adjustments:
      warmup_epochs: 20  # 更长的warmup
      
  # 医学图像
  medical:
    optimizer_adjustments:
      lr_multiplier: 0.3
      weight_decay_multiplier: 1.5
      
  # 自然场景
  natural_scenes:
    optimizer_adjustments:
      lr_multiplier: 1.0
      weight_decay_multiplier: 1.0

# 训练策略建议
training_strategies:
  
  # 长时间训练 (>200 epochs)
  long_training:
    use_warmup: true
    use_cosine_annealing: true
    early_stopping_patience: 50
    
  # 快速实验 (<50 epochs)
  quick_experiment:
    use_warmup: false
    use_step_lr: true
    early_stopping_patience: 10
    
  # 精细调优
  fine_tuning:
    use_small_lr: true
    use_reduce_on_plateau: true
    monitor_metric: "val_miou"

# 使用说明
usage_instructions: |
  1. 根据您的模型架构选择对应的配置：
     - Transformer: transformer_setup
     - CNN: cnn_setup
     - 混合: hybrid_setup
     - 轻量级: lightweight_setup
     
  2. 根据数据集类型调整参数：
     - 遥感: remote_sensing
     - 医学: medical
     - 自然场景: natural_scenes
     
  3. 根据训练时长选择策略：
     - 长时间: long_training
     - 快速: quick_experiment
     - 精调: fine_tuning
     
  4. 使用方式：
     python train.py experiment=segmentation_best_practices
     model=swin_unet optimizer=transformer_setup.optimizer
     scheduler=transformer_setup.scheduler
