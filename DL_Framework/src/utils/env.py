"""
环境设置工具模块
提供设置随机种子等环境相关的功能
"""

import logging
import random
import platform
import psutil
import re
import subprocess
import sys
from importlib import metadata

import numpy as np
import torch
import torchvision

try:
    import cpuinfo
except ImportError:
    cpuinfo = None

try:
    import rich
except ImportError:
    rich = None


def set_seed(seed: int):
    """
    设置全局随机种子以确保实验的可复现性。

    Args:
        seed (int): 要设置的随机种子。
    """
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        
        # 启用CUDNN的确定性模式，这可能会牺牲一些性能
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        
        logging.info(f"全局随机种子已设置为: {seed}")
        logging.warning("CUDNN确定性模式已开启，可能会影响训练速度。")
    else:
        logging.info("未设置随机种子，实验结果可能不具有可复现性。")


def _get_nvidia_driver_version() -> str:
    """通过 nvidia-smi 获取 NVIDIA 驱动版本"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, check=True, encoding='utf-8')
        # 使用正则表达式匹配版本号
        match = re.search(r"Driver Version:\s*(\d+\.\d+\.\d+)", result.stdout)
        if match:
            return match.group(1)
        return "N/A"
    except (FileNotFoundError, subprocess.CalledProcessError, UnicodeDecodeError):
        return "N/A (nvidia-smi not found or failed)"

def _format_cudnn_version(version_int: int) -> str:
    """将 cuDNN 版本号从整数格式化为点分字符串，例如 90100 -> 9.1.0"""
    if not isinstance(version_int, int):
        return str(version_int)
    major = version_int // 10000
    minor = (version_int % 10000) // 100
    patch = version_int % 100
    # cuDNN version format has changed in recent pytorch, adapting
    # example: 90100 -> 9.1.0
    if major > 100: # for older formats like 8700
        major = version_int // 1000
        minor = (version_int % 1000) // 100
        patch = version_int % 100

    return f"{major}.{minor}.{patch}"


def log_environment_info():
    """
    记录并打印详细的系统、硬件和软件环境信息。
    """
    logger = logging.getLogger(__name__)

    header = " 环境信息 "
    logger.info(f"{'='*20}{header}{'='*20}")

    # 1. 系统和硬件信息
    logger.info(f"操作系统: {platform.system()} {platform.release()}")
    if cpuinfo:
        cpu_brand = cpuinfo.get_cpu_info().get('brand_raw', 'N/A')
        logger.info(f"CPU: {cpu_brand} ({psutil.cpu_count(logical=True)} cores)")
    else:
        logger.info(f"CPU: {platform.processor()} ({psutil.cpu_count(logical=True)} cores) "
                    f"[提示: pip install py-cpuinfo 可显示更详细的CPU型号]")
    
    mem_total_gb = psutil.virtual_memory().total / (1024**3)
    logger.info(f"系统内存: {mem_total_gb:.2f} GB")

    # 2. Python 和依赖库信息
    logger.info("-------------------- Python & 依赖库 --------------------")
    logger.info(f"Python 版本: {platform.python_version()}")
    logger.info(f"PyTorch 版本: {torch.__version__}")
    logger.info(f"Torchvision 版本: {torchvision.__version__}")
    logger.info(f"NumPy 版本: {np.__version__}")
    if rich:
        try:
            rich_version = metadata.version("rich")
            logger.info(f"Rich 版本: {rich_version}")
        except metadata.PackageNotFoundError:
            logger.info("Rich 版本: N/A (未找到)")
    logger.info(f"psutil 版本: {psutil.__version__}")


    # 3. CUDA 和 GPU 信息
    logger.info("---------------------- CUDA & GPU -----------------------")
    if torch.cuda.is_available():
        logger.info("使用设备: CUDA (GPU)")
        
        driver_version = _get_nvidia_driver_version()
        logger.info(f"NVIDIA 驱动版本: {driver_version}")
        
        logger.info(f"CUDA 版本: {torch.version.cuda}")
        logger.info(f"可用GPU数量: {torch.cuda.device_count()}")
        
        if torch.backends.cudnn.is_available():
            logger.info(f"cuDNN 版本: {_format_cudnn_version(torch.backends.cudnn.version())}")
            logger.info(f"cuDNN 状态: {'启用' if torch.backends.cudnn.enabled else '禁用'}")
        else:
            logger.info("cuDNN: 不可用")

        for i in range(torch.cuda.device_count()):
            prop = torch.cuda.get_device_properties(i)
            logger.info(f"  - GPU {i}: {prop.name}")
            logger.info(f"    - 显存总量: {prop.total_memory / (1024**3):.2f} GB")
            logger.info(f"    - 计算能力: {prop.major}.{prop.minor}")

    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        logger.info("使用设备: MPS (Apple Silicon)")
    else:
        logger.info("使用设备: CPU")

    logger.info(f"{'=' * (42 + len(header))}") 