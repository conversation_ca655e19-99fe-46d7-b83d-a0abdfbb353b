"""
日志设置和处理模块
支持控制台和文件输出，带有不同级别的日志记录
"""

import datetime
import json
import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import torch
from rich.logging import RichHandler


def setup_logging(
    log_file: Optional[Union[str, Path]] = None,
    log_level: Union[str, int] = logging.INFO,
    console: bool = True,
    file_log: bool = True,
    propagate: bool = False,
    file_mode: str = 'a',
    date_in_filename: bool = True
) -> logging.Logger:
    """
    设置日志记录器，使用RichHandler以获得更美观的控制台输出。
    
    Args:
        log_file: 日志文件路径，如果为None则自动生成。
        log_level: 日志级别，可以是字符串或整数。
        console: 是否输出到控制台。
        file_log: 是否输出到文件。
        propagate: 是否传播日志到父记录器。
        file_mode: 文件打开模式，'a'为追加，'w'为覆盖。
        date_in_filename: 是否在日志文件名中添加日期。
        
    Returns:
        配置好的日志记录器。
    """
    # 确定日志级别
    if isinstance(log_level, str):
        log_level = getattr(logging, log_level.upper())
    
    # 获取根记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    logger.propagate = propagate
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 定义基础日志格式
    # 对于RichHandler，它有自己的美化逻辑；对于FileHandler，这是纯文本格式
    log_format = "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    
    # 添加控制台处理器 (使用RichHandler)
    if console:
        console_handler = RichHandler(
            rich_tracebacks=True,
            tracebacks_suppress=[torch], # 在rich中隐藏冗长的torch内部堆栈跟踪
            markup=True,
            log_time_format="[%X]"
        )
        console_handler.setLevel(log_level)
        # RichHandler有自己推荐的格式，不需要formatter
        logger.addHandler(console_handler)
    
    # 添加文件处理器 (保持标准的FileHandler以输出纯文本)
    if file_log:
        if log_file is None:
            # 确保日志目录存在
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            if date_in_filename:
                date_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                log_name = f"app_{date_str}.log"
            else:
                log_name = "app.log"
            
            log_file = log_dir / log_name
        
        log_file_str = str(log_file)
        os.makedirs(os.path.dirname(os.path.abspath(log_file_str)), exist_ok=True)
        file_handler = logging.FileHandler(log_file_str, mode=file_mode)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(logging.Formatter(log_format, datefmt=date_format))
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取指定名称的日志记录器，如果不存在则创建
    
    Args:
        name: 日志记录器名称
        
    Returns:
        获取到的日志记录器
    """
    return logging.getLogger(name)