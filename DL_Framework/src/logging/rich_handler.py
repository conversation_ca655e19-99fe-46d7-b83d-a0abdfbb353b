"""
Rich 日志处理器模块

封装了所有使用 Rich 库进行美观终端输出的功能。
- 自定义进度条
- 系统资源监控
- 训练总结面板
"""

import time
import psutil
import torch
from rich.panel import Panel
from rich.table import Table
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    MofNCompleteColumn,
    TimeRemainingColumn,
    TimeElapsedColumn,
)

# 尝试导入 pynvml
try:
    import pynvml
    pynvml.nvmlInit()
    PYNVML_AVAILABLE = True
except (ImportError, pynvml.NVMLError):
    pynvml = None
    PYNVML_AVAILABLE = False


class RichProgressBar(Progress):
    """自定义的富文本进度条，用于美化训练过程的输出"""
    def __init__(self, *args, **kwargs):
        super().__init__(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            "•",
            TimeElapsedColumn(),
            "<",
            TimeRemainingColumn(),
            "•",
            TextColumn(
                "[yellow]Loss: {task.fields[loss]:.4f}[/yellow] | "
                "[green]Best mIoU: {task.fields[best_miou]:.4f}[/green]"
                "{task.fields[system_stats]}"
            ),
            *args,
            **kwargs,
        )


def get_system_stats(device: torch.device) -> str:
    """获取当前系统资源使用情况并格式化为字符串"""
    # CPU 和 RAM
    cpu_percent = psutil.cpu_percent()
    ram = psutil.virtual_memory()
    ram_used_gb = ram.used / (1024**3)
    ram_total_gb = ram.total / (1024**3)
    
    # GPU 和 VRAM
    gpu_percent_str = ""
    vram_str = ""
    if PYNVML_AVAILABLE and device.type == 'cuda':
        try:
            device_index = device.index if device.index is not None else torch.cuda.current_device()
            handle = pynvml.nvmlDeviceGetHandleByIndex(device_index)
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_percent_str = f"[magenta]GPU {util.gpu: >3}%[/magenta]"
            mem = pynvml.nvmlDeviceGetMemoryInfo(handle)
            vram_used_gb = mem.used / (1024**3)
            vram_total_gb = mem.total / (1024**3)
            vram_str = f"[cyan]VRAM {vram_used_gb: >4.1f}/{vram_total_gb:.1f}G[/cyan]"
        except (pynvml.NVMLError, AttributeError):
            gpu_percent_str = "[red]GPU N/A[/red]"
            vram_str = "[red]VRAM N/A[/red]"

    # 组合字符串
    ram_str = f"[blue]RAM {ram_used_gb: >4.1f}/{ram_total_gb:.1f}G[/blue]"
    cpu_str = f"[bright_blue]CPU {int(cpu_percent): >3}%[/bright_blue]"
    parts = [gpu_percent_str, vram_str, cpu_str, ram_str]
    return " | ".join(p for p in parts if p)


def create_summary_panel(logs: dict, total_duration_seconds: float) -> Panel:
    """创建并返回一个包含训练最终总结的Rich Panel"""
    total_duration_str = time.strftime('%H:%M:%S', time.gmtime(total_duration_seconds))

    summary_table = Table(show_header=False, box=None, padding=(0, 2))
    summary_table.add_column("Metric", style="cyan")
    summary_table.add_column("Value", style="bold magenta")

    summary_table.add_row("总训练耗时", total_duration_str)
    if 'train_loss' in logs:
        summary_table.add_row("最终训练损失", f"{logs['train_loss']:.4f}")
    if 'val_loss' in logs:
        summary_table.add_row("最终验证损失", f"{logs['val_loss']:.4f}")
    if 'miou' in logs:
        summary_table.add_row("最终验证 mIoU", f"{logs['miou']:.4f}")
    if 'accuracy' in logs:
        summary_table.add_row("最终验证 Acc", f"{logs['accuracy']:.4f}")

    summary_panel = Panel(
        summary_table,
        title="[bold green]训练总结[/bold green]",
        border_style="green",
        expand=False,
        padding=(1, 2)
    )
    return summary_panel

def cleanup_pynvml():
    """清理 pynvml 资源"""
    if PYNVML_AVAILABLE and pynvml:
        try:
            pynvml.nvmlShutdown()
        except pynvml.NVMLError:
            pass # 如果已经关闭或初始化失败，则忽略
